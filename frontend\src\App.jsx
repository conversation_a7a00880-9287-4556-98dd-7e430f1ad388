import { Routes, Route } from 'react-router-dom'
import { useState } from 'react'
import Navbar from './components/Navbar'
import Home from './pages/Home'
import AssetList from './pages/AssetList'
import AssetDetail from './pages/AssetDetail'
import AssetForm from './pages/AssetForm'
import NotFound from './pages/NotFound'
import Login from './pages/Login'
import './App.css'

function App() {
  return (
    <div className="app">
      <Navbar />
      <main className="container">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/assets" element={<AssetList />} />
          <Route path="/assets/:id" element={<AssetDetail />} />
          <Route path="/assets/new" element={<AssetForm />} />
          <Route path="/assets/edit/:id" element={<AssetForm />} />
          <Route path="/login" element={<Login />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </main>
    </div>
  )
}

export default App
