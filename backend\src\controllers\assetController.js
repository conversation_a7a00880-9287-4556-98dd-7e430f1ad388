const Asset = require('../models/Asset');
const csv = require('csv-parser');
const fs = require('fs');
const { protect, authorize } = require('../middleware/auth');

// @desc    Get all assets
// @route   GET /api/assets
// @access  Public
exports.getAssets = async (req, res) => {
  try {
    const assets = await Asset.find();
    res.status(200).json({
      success: true,
      count: assets.length,
      data: assets
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// @desc    Get single asset
// @route   GET /api/assets/:id
// @access  Public
exports.getAsset = async (req, res) => {
  try {
    const asset = await Asset.findById(req.params.id);
    
    if (!asset) {
      return res.status(404).json({
        success: false,
        error: 'Asset not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: asset
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Helper to create a history entry
function createHistoryEntry(asset, user, changeType) {
  return {
    changedBy: user.username,
    changeType,
    timestamp: new Date(),
    snapshot: {
      assetTag: asset.assetTag,
      status: asset.status,
      manufacturer: asset.manufacturer,
      model: asset.model,
      assetType: asset.assetType,
      serialNumber: asset.serialNumber,
      currentOwner: asset.currentOwner,
      remarks: asset.remarks
    }
  };
}

// @desc    Create new asset
// @route   POST /api/assets
// @access  Private (Super Admin only)
exports.createAsset = [protect, authorize('superAdmin'), async (req, res) => {
  try {
    const asset = await Asset.create(req.body);
    // Add history entry
    asset.history.push(createHistoryEntry(asset, req.user, 'created'));
    await asset.save();
    res.status(201).json({
      success: true,
      data: asset
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Server Error'
      });
    }
  }
}];

// @desc    Update asset
// @route   PUT /api/assets/:id
// @access  Private (Super Admin only)
exports.updateAsset = [protect, authorize('superAdmin'), async (req, res) => {
  try {
    const asset = await Asset.findById(req.params.id);
    if (!asset) {
      return res.status(404).json({
        success: false,
        error: 'Asset not found'
      });
    }
    // Update fields
    Object.assign(asset, req.body);
    // Add history entry
    asset.history.push(createHistoryEntry(asset, req.user, 'updated'));
    await asset.save();
    res.status(200).json({
      success: true,
      data: asset
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Server Error'
      });
    }
  }
}];

// @desc    Delete asset
// @route   DELETE /api/assets/:id
// @access  Private (Super Admin only)
exports.deleteAsset = [protect, authorize('superAdmin'), async (req, res) => {
  try {
    const asset = await Asset.findById(req.params.id);
    if (!asset) {
      return res.status(404).json({
        success: false,
        error: 'Asset not found'
      });
    }
    // Add history entry before deletion
    asset.history.push(createHistoryEntry(asset, req.user, 'deleted'));
    await asset.save();
    await asset.deleteOne();
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
}];

// @desc    Get asset history
// @route   GET /api/assets/:id/history
// @access  Private (Super Admin only)
exports.getAssetHistory = [protect, authorize('superAdmin'), async (req, res) => {
  try {
    const asset = await Asset.findById(req.params.id);
    if (!asset) {
      return res.status(404).json({
        success: false,
        error: 'Asset not found'
      });
    }
    res.status(200).json({
      success: true,
      history: asset.history
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
}];

// @desc    Import assets from CSV
// @route   POST /api/assets/import
// @access  Private (Super Admin only)
exports.importAssetsFromCSV = [protect, authorize('superAdmin'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: 'Please upload a CSV file'
    });
  }

  const results = [];
  fs.createReadStream(req.file.path)
    .pipe(csv())
    .on('data', (data) => results.push(data))
    .on('end', async () => {
      try {
        const assets = await Asset.insertMany(results);
        res.status(201).json({
          success: true,
          count: assets.length,
          data: assets
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: 'Error importing assets'
        });
      }
    });
}];
