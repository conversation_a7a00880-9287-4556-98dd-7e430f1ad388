#!/bin/bash

# Asset Management Application Deployment Script
# This script deploys the application using Docker Compose

set -e

echo "🚀 Starting Asset Management Application Deployment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Set deployment mode (development or production)
DEPLOYMENT_MODE=${1:-development}

echo "📋 Deployment mode: $DEPLOYMENT_MODE"

# Load environment variables
if [ "$DEPLOYMENT_MODE" = "production" ]; then
    if [ -f ".env.production" ]; then
        export $(cat .env.production | grep -v '^#' | xargs)
        COMPOSE_FILE="docker-compose.prod.yml"
    else
        echo "❌ .env.production file not found. Please create it first."
        exit 1
    fi
else
    COMPOSE_FILE="docker-compose.yml"
fi

echo "📁 Using compose file: $COMPOSE_FILE"

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f $COMPOSE_FILE down

# Remove old images (optional)
read -p "🗑️  Do you want to remove old images? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 Removing old images..."
    docker system prune -f
fi

# Build and start containers
echo "🔨 Building and starting containers..."
docker-compose -f $COMPOSE_FILE up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 30

# Check service health
echo "🔍 Checking service health..."
docker-compose -f $COMPOSE_FILE ps

# Run tests if in development mode
if [ "$DEPLOYMENT_MODE" = "development" ]; then
    echo "🧪 Running tests..."
    
    # Backend tests
    echo "Testing backend..."
    docker-compose -f $COMPOSE_FILE exec backend npm test
    
    # Frontend tests
    echo "Testing frontend..."
    docker-compose -f $COMPOSE_FILE exec frontend npm test
fi

echo "✅ Deployment completed successfully!"
echo ""
echo "🌐 Application URLs:"
if [ "$DEPLOYMENT_MODE" = "production" ]; then
    echo "   Frontend: https://$DOMAIN"
    echo "   Backend API: https://$DOMAIN/api"
else
    echo "   Frontend: http://localhost:3000"
    echo "   Backend API: http://localhost:5000"
fi
echo ""
echo "📊 Default admin credentials:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "⚠️  IMPORTANT: Change the default admin password after first login!"

# Show logs
read -p "📜 Do you want to view the logs? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker-compose -f $COMPOSE_FILE logs -f
fi
