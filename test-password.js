const bcrypt = require('bcryptjs');

// Test the password hash from the database
const storedHash = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';
const testPassword = 'admin123';

console.log('Testing password verification...');
console.log('Password:', testPassword);
console.log('Stored hash:', storedHash);

bcrypt.compare(testPassword, storedHash, (err, result) => {
    if (err) {
        console.error('Error:', err);
    } else {
        console.log('Password match:', result);
        if (result) {
            console.log('✅ Password verification successful!');
        } else {
            console.log('❌ Password verification failed!');
            // Generate correct hash
            bcrypt.hash(testPassword, 10, (err, hash) => {
                if (err) {
                    console.error('Hash generation error:', err);
                } else {
                    console.log('Correct hash for admin123:', hash);
                }
            });
        }
    }
});
