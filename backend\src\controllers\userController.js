const User = require('../models/User');
const bcrypt = require('bcryptjs');

// @desc    Create super admin account
// @route   POST /api/users/superadmin
// @access  Public
exports.createSuperAdmin = async (req, res) => {
  try {
    const { username, password } = req.body;
    const superAdmin = await User.create({
      username,
      password,
      role: 'superAdmin'
    });
    res.status(201).json({
      success: true,
      data: superAdmin
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Error creating super admin'
    });
  }
};

// @desc    Create new user account
// @route   POST /api/users
// @access  Private (Super Admin only)
exports.createUser = async (req, res) => {
  try {
    const { username, password } = req.body;
    const user = await User.create({
      username,
      password,
      role: 'user'
    });
    res.status(201).json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Error creating user'
    });
  }
}; 