const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const User = require('../src/models/User');
const Asset = require('../src/models/Asset');
const assetRoutes = require('../src/routes/assets');
const authRoutes = require('../src/routes/auth');

const app = express();
app.use(express.json());
app.use('/api/auth', authRoutes);
app.use('/api/assets', assetRoutes);

describe('Asset Management Tests', () => {
  let userToken, adminToken;
  let testAsset;

  beforeEach(async () => {
    // Create test users
    const user = await User.create({
      username: 'testuser',
      password: 'password123',
      role: 'user'
    });

    const admin = await User.create({
      username: 'testadmin',
      password: 'password123',
      role: 'superAdmin'
    });

    // Get tokens
    const userLogin = await request(app)
      .post('/api/auth/login')
      .send({ username: 'testuser', password: 'password123' });
    userToken = userLogin.body.token;

    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({ username: 'testadmin', password: 'password123' });
    adminToken = adminLogin.body.token;

    // Create test asset
    testAsset = await Asset.create({
      assetTag: 'TEST001',
      status: 'Available',
      manufacturer: 'Test Manufacturer',
      model: 'Test Model',
      assetType: 'Desktop',
      serialNumber: 'SN123456',
      currentOwner: 'Test Owner',
      remarks: 'Test remarks'
    });
  });

  describe('GET /api/assets', () => {
    it('should get all assets with valid token', async () => {
      const response = await request(app)
        .get('/api/assets')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.count).toBe(1);
    });

    it('should reject request without token', async () => {
      const response = await request(app)
        .get('/api/assets');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/assets/:id', () => {
    it('should get specific asset with valid token', async () => {
      const response = await request(app)
        .get(`/api/assets/${testAsset._id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.assetTag).toBe('TEST001');
    });

    it('should return 404 for non-existent asset', async () => {
      const fakeId = new mongoose.Types.ObjectId();
      const response = await request(app)
        .get(`/api/assets/${fakeId}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/assets', () => {
    const newAsset = {
      assetTag: 'TEST002',
      status: 'Available',
      manufacturer: 'New Manufacturer',
      model: 'New Model',
      assetType: 'Laptop',
      serialNumber: 'SN789012',
      currentOwner: 'New Owner',
      remarks: 'New remarks'
    };

    it('should create asset with admin token', async () => {
      const response = await request(app)
        .post('/api/assets')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newAsset);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.assetTag).toBe('TEST002');
    });

    it('should reject creation with user token', async () => {
      const response = await request(app)
        .post('/api/assets')
        .set('Authorization', `Bearer ${userToken}`)
        .send(newAsset);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/assets')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should reject duplicate asset tag', async () => {
      const duplicateAsset = { ...newAsset, assetTag: 'TEST001' };
      const response = await request(app)
        .post('/api/assets')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(duplicateAsset);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/assets/:id', () => {
    it('should update asset with admin token', async () => {
      const updateData = { status: 'Assigned', currentOwner: 'Updated Owner' };
      const response = await request(app)
        .put(`/api/assets/${testAsset._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('Assigned');
      expect(response.body.data.currentOwner).toBe('Updated Owner');
    });

    it('should reject update with user token', async () => {
      const response = await request(app)
        .put(`/api/assets/${testAsset._id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({ status: 'Assigned' });

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /api/assets/:id', () => {
    it('should delete asset with admin token', async () => {
      const response = await request(app)
        .delete(`/api/assets/${testAsset._id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify asset is deleted
      const deletedAsset = await Asset.findById(testAsset._id);
      expect(deletedAsset).toBeNull();
    });

    it('should reject deletion with user token', async () => {
      const response = await request(app)
        .delete(`/api/assets/${testAsset._id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });
});
