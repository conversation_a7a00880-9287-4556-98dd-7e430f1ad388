<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>IT Asset Management System</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    header {
      background-color: #333;
      color: white;
      padding: 20px 0;
      text-align: center;
    }

    h1 {
      margin: 0;
    }

    .asset-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .asset-card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 20px;
      transition: transform 0.3s ease;
    }

    .asset-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .asset-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }

    .asset-header i {
      font-size: 1.5rem;
      margin-right: 10px;
      color: #007bff;
    }

    .asset-header h3 {
      margin: 0;
      color: #333;
    }

    .asset-details {
      margin-bottom: 15px;
    }

    .status-badge {
      display: inline-block;
      padding: 3px 8px;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: bold;
      color: white;
    }

    .status-available {
      background-color: #28a745;
    }

    .status-assigned {
      background-color: #fd7e14;
    }

    .status-maintenance {
      background-color: #dc3545;
    }

    .status-retired {
      background-color: #6c757d;
    }

    .asset-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
    }

    .btn {
      padding: 8px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }

    .btn-primary {
      background-color: #007bff;
      color: white;
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-danger {
      background-color: #dc3545;
      color: white;
    }

    footer {
      background-color: #333;
      color: white;
      text-align: center;
      padding: 10px 0;
      margin-top: 40px;
    }

    /* Modal styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .modal-content {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      max-width: 600px;
      width: 90%;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
    }

    .close-modal {
      position: absolute;
      top: 10px;
      right: 15px;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }

    .close-modal:hover {
      color: #000;
    }

    .asset-view-section {
      margin-bottom: 25px;
      border-bottom: 1px solid #eee;
      padding-bottom: 15px;
    }

    .asset-view-section:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .asset-view-section h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #333;
      font-size: 1.1rem;
    }

    .asset-details-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }

    .asset-details {
      margin-top: 20px;
    }

    /* Form styles */
    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }

    .form-group textarea {
      height: 100px;
      resize: vertical;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
    }

    .form-section {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
      border: 1px solid #eee;
    }

    .form-section h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #333;
      font-size: 1.1rem;
    }

    /* Add button */
    .add-asset-btn {
      background-color: #28a745;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 10px 15px;
      font-size: 16px;
      cursor: pointer;
      margin-bottom: 20px;
    }

    .add-asset-btn:hover {
      background-color: #218838;
    }

    /* Search and filter styles */
    .filters {
      margin: 20px 0;
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      align-items: center;
    }

    .search-box {
      display: flex;
      flex: 1;
      min-width: 250px;
    }

    .search-box input {
      flex: 1;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px 0 0 4px;
      font-size: 16px;
    }

    .search-box button {
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 0 4px 4px 0;
      padding: 10px 15px;
      cursor: pointer;
    }

    .filter-options {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .filter-options select {
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
      min-width: 150px;
    }

    /* Dashboard styles */
    .dashboard {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .dashboard-card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 20px;
      display: flex;
      align-items: center;
      transition: transform 0.3s ease;
    }

    .dashboard-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .dashboard-icon {
      font-size: 2.5rem;
      margin-right: 15px;
    }

    .dashboard-info h3 {
      margin: 0 0 5px 0;
      font-size: 1.1rem;
      color: #666;
    }

    .dashboard-info p {
      margin: 0;
      font-size: 1.8rem;
      font-weight: bold;
      color: #333;
    }

    .total-assets .dashboard-icon {
      color: #007bff;
    }

    .laptop-assets .dashboard-icon {
      color: #28a745;
    }

    .desktop-assets .dashboard-icon {
      color: #fd7e14;
    }

    .server-assets .dashboard-icon {
      color: #dc3545;
    }

    .no-results {
      text-align: center;
      padding: 20px;
      font-size: 1.2rem;
      color: #666;
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <h1><i class="fas fa-laptop"></i> IT Asset Management System</h1>
    </div>
  </header>

  <div class="container">
    <!-- Dashboard -->
    <div class="dashboard">
      <div class="dashboard-card total-assets">
        <i class="fas fa-desktop dashboard-icon"></i>
        <div class="dashboard-info">
          <h3>Total IT Assets</h3>
          <p id="total-assets-count">0</p>
        </div>
      </div>

      <div class="dashboard-card laptop-assets">
        <i class="fas fa-laptop dashboard-icon"></i>
        <div class="dashboard-info">
          <h3>Laptops</h3>
          <p id="laptop-assets-count">0</p>
        </div>
      </div>

      <div class="dashboard-card desktop-assets">
        <i class="fas fa-desktop dashboard-icon"></i>
        <div class="dashboard-info">
          <h3>Desktops</h3>
          <p id="desktop-assets-count">0</p>
        </div>
      </div>

      <div class="dashboard-card server-assets">
        <i class="fas fa-server dashboard-icon"></i>
        <div class="dashboard-info">
          <h3>Servers</h3>
          <p id="server-assets-count">0</p>
        </div>
      </div>
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 30px;">
      <h2>Asset List</h2>
      <button class="add-asset-btn" onclick="showAddAssetForm()">
        <i class="fas fa-plus"></i> Add New Asset
      </button>
    </div>

    <div class="filters">
      <div class="search-box">
        <input type="text" id="search-input" placeholder="Search assets...">
        <button onclick="searchAssets()"><i class="fas fa-search"></i></button>
      </div>

      <div class="filter-options">
        <select id="category-filter" onchange="filterAssets()">
          <option value="">All Categories</option>
          <option value="Laptop">Laptop</option>
          <option value="Desktop">Desktop</option>
          <option value="Server">Server</option>
        </select>

        <select id="status-filter" onchange="filterAssets()">
          <option value="">All Statuses</option>
          <option value="Available">Available</option>
          <option value="Assigned">Assigned</option>
          <option value="Maintenance">Maintenance</option>
          <option value="Retired">Retired</option>
        </select>
      </div>
    </div>

    <div id="loading">Loading assets...</div>
    <div id="asset-list" class="asset-list"></div>
  </div>

  <footer>
    <div class="container">
      <p>&copy; 2025 IT Asset Management System | <i class="fas fa-laptop"></i> Laptops | <i class="fas fa-desktop"></i> Desktops | <i class="fas fa-server"></i> Servers</p>
    </div>
  </footer>

  <script>
    // Function to fetch assets from the API
    async function fetchAssets() {
      try {
        const response = await fetch('http://localhost:5000/api/assets');
        const result = await response.json();
        return result.data;
      } catch (error) {
        console.error('Error fetching assets:', error);
        return [];
      }
    }

    // Function to render assets
    async function renderAssets() {
      const assetListElement = document.getElementById('asset-list');
      const loadingElement = document.getElementById('loading');

      try {
        // Fetch assets from the API
        const assets = await fetchAssets();

        // Hide loading indicator
        loadingElement.style.display = 'none';

        if (assets.length === 0) {
          assetListElement.innerHTML = '<p>No assets found.</p>';
          return;
        }

        // Render each asset
        assets.forEach(asset => {
          const assetCard = document.createElement('div');
          assetCard.className = 'asset-card';

          // Format the purchase date
          const purchaseDate = new Date(asset.purchaseDate).toLocaleDateString();

          // Choose icon based on category
          let categoryIcon = '';
          switch(asset.category) {
            case 'Laptop':
              categoryIcon = '<i class="fas fa-laptop"></i>';
              break;
            case 'Desktop':
              categoryIcon = '<i class="fas fa-desktop"></i>';
              break;
            case 'Server':
              categoryIcon = '<i class="fas fa-server"></i>';
              break;
            default:
              categoryIcon = '<i class="fas fa-desktop"></i>';
          }

          assetCard.innerHTML = `
            <div class="asset-header">
              ${categoryIcon} <h3>${asset.name}</h3>
            </div>
            <div class="asset-details">
              <p><strong>Category:</strong> ${asset.category}</p>
              <p><strong>Manufacturer:</strong> ${asset.manufacturer || 'N/A'}</p>
              <p><strong>Model:</strong> ${asset.model || 'N/A'}</p>
              <p><strong>Status:</strong> <span class="status-badge status-${asset.status.toLowerCase()}">${asset.status}</span></p>
              <p><strong>Serial Number:</strong> ${asset.serialNumber || 'N/A'}</p>
              ${asset.specifications && asset.specifications.processor ? `<p><strong>Processor:</strong> ${asset.specifications.processor}</p>` : ''}
              ${asset.specifications && asset.specifications.memory ? `<p><strong>Memory:</strong> ${asset.specifications.memory}</p>` : ''}
              ${asset.assignedTo ? `<p><strong>Assigned To:</strong> ${asset.assignedTo}</p>` : ''}
              <p><strong>Location:</strong> ${asset.location}</p>
            </div>
            <div class="asset-actions">
              <button class="btn btn-primary" onclick="viewAsset('${asset._id}')"><i class="fas fa-eye"></i> View</button>
              <button class="btn btn-secondary" onclick="editAsset('${asset._id}')"><i class="fas fa-edit"></i> Edit</button>
              <button class="btn btn-danger" onclick="deleteAsset('${asset._id}')"><i class="fas fa-trash"></i> Delete</button>
            </div>
          `;

          assetListElement.appendChild(assetCard);
        });
      } catch (error) {
        console.error('Error rendering assets:', error);
        loadingElement.style.display = 'none';
        assetListElement.innerHTML = '<p>Error loading assets. Please try again later.</p>';
      }
    }

    // Asset actions
    async function viewAsset(id) {
      try {
        const response = await fetch(`http://localhost:5000/api/assets/${id}`);
        const result = await response.json();

        if (result.success) {
          const asset = result.data;
          const purchaseDate = new Date(asset.purchaseDate).toLocaleDateString();

          // Choose icon based on category
          let categoryIcon = '';
          switch(asset.category) {
            case 'Laptop':
              categoryIcon = '<i class="fas fa-laptop"></i>';
              break;
            case 'Desktop':
              categoryIcon = '<i class="fas fa-desktop"></i>';
              break;
            case 'Server':
              categoryIcon = '<i class="fas fa-server"></i>';
              break;
            default:
              categoryIcon = '<i class="fas fa-desktop"></i>';
          }

          // Format warranty date if available
          const warrantyDate = asset.warrantyExpiryDate ? new Date(asset.warrantyExpiryDate).toLocaleDateString() : 'N/A';

          // Create modal for viewing asset details
          const modalHtml = `
            <div class="modal-overlay" id="view-modal">
              <div class="modal-content">
                <span class="close-modal" onclick="closeModal('view-modal')">&times;</span>
                <div class="asset-header">
                  ${categoryIcon} <h2>${asset.name}</h2>
                </div>

                <div class="asset-view-section">
                  <h3>Basic Information</h3>
                  <div class="asset-details-grid">
                    <div>
                      <p><strong>Description:</strong> ${asset.description || 'N/A'}</p>
                      <p><strong>Category:</strong> ${asset.category}</p>
                      <p><strong>Manufacturer:</strong> ${asset.manufacturer || 'N/A'}</p>
                      <p><strong>Model:</strong> ${asset.model || 'N/A'}</p>
                      <p><strong>Status:</strong> <span class="status-badge status-${asset.status.toLowerCase()}">${asset.status}</span></p>
                    </div>
                    <div>
                      <p><strong>Serial Number:</strong> ${asset.serialNumber || 'N/A'}</p>
                      <p><strong>Location:</strong> ${asset.location}</p>
                      <p><strong>Assigned To:</strong> ${asset.assignedTo || 'N/A'}</p>
                      <p><strong>Purchase Date:</strong> ${purchaseDate}</p>
                      <p><strong>Purchase Price:</strong> $${asset.purchasePrice}</p>
                      <p><strong>Warranty Expiry:</strong> ${warrantyDate}</p>
                    </div>
                  </div>
                </div>

                <div class="asset-view-section">
                  <h3>Technical Specifications</h3>
                  <div class="asset-details-grid">
                    <div>
                      <p><strong>Processor:</strong> ${asset.specifications && asset.specifications.processor ? asset.specifications.processor : 'N/A'}</p>
                      <p><strong>Memory:</strong> ${asset.specifications && asset.specifications.memory ? asset.specifications.memory : 'N/A'}</p>
                    </div>
                    <div>
                      <p><strong>Storage:</strong> ${asset.specifications && asset.specifications.storage ? asset.specifications.storage : 'N/A'}</p>
                      <p><strong>Operating System:</strong> ${asset.specifications && asset.specifications.operatingSystem ? asset.specifications.operatingSystem : 'N/A'}</p>
                    </div>
                  </div>
                </div>

                <div class="asset-view-section">
                  <h3>Network Information</h3>
                  <div class="asset-details-grid">
                    <div>
                      <p><strong>MAC Address:</strong> ${asset.macAddress || 'N/A'}</p>
                    </div>
                    <div>
                      <p><strong>IP Address:</strong> ${asset.ipAddress || 'N/A'}</p>
                    </div>
                  </div>
                </div>

                <div class="asset-view-section">
                  <h3>System Information</h3>
                  <div class="asset-details-grid">
                    <div>
                      <p><strong>Created:</strong> ${new Date(asset.createdAt).toLocaleString()}</p>
                    </div>
                    <div>
                      <p><strong>Last Updated:</strong> ${new Date(asset.updatedAt).toLocaleString()}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Add modal to the DOM
          document.body.insertAdjacentHTML('beforeend', modalHtml);

          // Show the modal
          document.getElementById('view-modal').style.display = 'flex';
        } else {
          alert('Error: Asset not found');
        }
      } catch (error) {
        console.error('Error viewing asset:', error);
        alert('Error viewing asset details. Please try again.');
      }
    }

    async function editAsset(id) {
      try {
        // Fetch the asset data
        const response = await fetch(`http://localhost:5000/api/assets/${id}`);
        const result = await response.json();

        if (result.success) {
          const asset = result.data;

          // Format the purchase date for the input field (YYYY-MM-DD)
          const purchaseDate = new Date(asset.purchaseDate).toISOString().split('T')[0];

          // Create modal for editing asset
          const modalHtml = `
            <div class="modal-overlay" id="edit-modal">
              <div class="modal-content">
                <span class="close-modal" onclick="closeModal('edit-modal')">&times;</span>
                <h2>Edit Asset</h2>
                <form id="edit-asset-form">
                  <div class="form-group">
                    <label for="name">Name:</label>
                    <input type="text" id="name" name="name" value="${asset.name}" required>
                  </div>
                  <div class="form-group">
                    <label for="description">Description:</label>
                    <textarea id="description" name="description">${asset.description || ''}</textarea>
                  </div>
                  <div class="form-group">
                    <label for="category">Category:</label>
                    <select id="category" name="category" required>
                      <option value="Hardware" ${asset.category === 'Hardware' ? 'selected' : ''}>Hardware</option>
                      <option value="Software" ${asset.category === 'Software' ? 'selected' : ''}>Software</option>
                      <option value="Furniture" ${asset.category === 'Furniture' ? 'selected' : ''}>Furniture</option>
                      <option value="Vehicle" ${asset.category === 'Vehicle' ? 'selected' : ''}>Vehicle</option>
                      <option value="Other" ${asset.category === 'Other' ? 'selected' : ''}>Other</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="status">Status:</label>
                    <select id="status" name="status" required>
                      <option value="Available" ${asset.status === 'Available' ? 'selected' : ''}>Available</option>
                      <option value="Assigned" ${asset.status === 'Assigned' ? 'selected' : ''}>Assigned</option>
                      <option value="Maintenance" ${asset.status === 'Maintenance' ? 'selected' : ''}>Maintenance</option>
                      <option value="Retired" ${asset.status === 'Retired' ? 'selected' : ''}>Retired</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="location">Location:</label>
                    <input type="text" id="location" name="location" value="${asset.location}" required>
                  </div>
                  <div class="form-group">
                    <label for="assignedTo">Assigned To:</label>
                    <input type="text" id="assignedTo" name="assignedTo" value="${asset.assignedTo || ''}">
                  </div>
                  <div class="form-group">
                    <label for="serialNumber">Serial Number:</label>
                    <input type="text" id="serialNumber" name="serialNumber" value="${asset.serialNumber || ''}">
                  </div>
                  <div class="form-group">
                    <label for="purchaseDate">Purchase Date:</label>
                    <input type="date" id="purchaseDate" name="purchaseDate" value="${purchaseDate}" required>
                  </div>
                  <div class="form-group">
                    <label for="purchasePrice">Purchase Price ($):</label>
                    <input type="number" id="purchasePrice" name="purchasePrice" value="${asset.purchasePrice}" required>
                  </div>
                  <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('edit-modal')">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveAsset('${id}')">Save Changes</button>
                  </div>
                </form>
              </div>
            </div>
          `;

          // Add modal to the DOM
          document.body.insertAdjacentHTML('beforeend', modalHtml);

          // Show the modal
          document.getElementById('edit-modal').style.display = 'flex';
        } else {
          alert('Error: Asset not found');
        }
      } catch (error) {
        console.error('Error editing asset:', error);
        alert('Error loading asset details for editing. Please try again.');
      }
    }

    async function saveAsset(id) {
      try {
        // Get form data
        const form = document.getElementById('edit-asset-form');
        const formData = {
          name: form.name.value,
          description: form.description.value,
          category: form.category.value,
          status: form.status.value,
          location: form.location.value,
          assignedTo: form.assignedTo.value,
          serialNumber: form.serialNumber.value,
          purchaseDate: form.purchaseDate.value,
          purchasePrice: parseFloat(form.purchasePrice.value)
        };

        // Send PUT request to update the asset
        const response = await fetch(`http://localhost:5000/api/assets/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
          alert('Asset updated successfully!');
          closeModal('edit-modal');

          // Refresh the asset list
          const assetListElement = document.getElementById('asset-list');
          assetListElement.innerHTML = '';
          document.getElementById('loading').style.display = 'block';
          await renderAssets();
        } else {
          alert(`Error: ${result.error}`);
        }
      } catch (error) {
        console.error('Error saving asset:', error);
        alert('Error saving asset changes. Please try again.');
      }
    }

    async function deleteAsset(id) {
      if (confirm('Are you sure you want to delete this asset? This action cannot be undone.')) {
        try {
          const response = await fetch(`http://localhost:5000/api/assets/${id}`, {
            method: 'DELETE'
          });

          const result = await response.json();

          if (result.success) {
            alert('Asset deleted successfully!');

            // Refresh the asset list
            const assetListElement = document.getElementById('asset-list');
            assetListElement.innerHTML = '';
            document.getElementById('loading').style.display = 'block';
            await renderAssets();
          } else {
            alert(`Error: ${result.error}`);
          }
        } catch (error) {
          console.error('Error deleting asset:', error);
          alert('Error deleting asset. Please try again.');
        }
      }
    }

    function closeModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.remove();
      }
    }

    // Add new asset function
    function showAddAssetForm() {
      // Create modal for adding a new asset
      const modalHtml = `
        <div class="modal-overlay" id="add-modal">
          <div class="modal-content">
            <span class="close-modal" onclick="closeModal('add-modal')">&times;</span>
            <h2>Add New IT Asset</h2>
            <form id="add-asset-form">
              <div class="form-group">
                <label for="name">Asset Name:</label>
                <input type="text" id="name" name="name" placeholder="e.g., Development Laptop #12" required>
              </div>
              <div class="form-group">
                <label for="description">Description:</label>
                <textarea id="description" name="description" placeholder="Brief description of the asset"></textarea>
              </div>
              <div class="form-group">
                <label for="category">Category:</label>
                <select id="category" name="category" required>
                  <option value="">Select a category</option>
                  <option value="Laptop">Laptop</option>
                  <option value="Desktop">Desktop</option>
                  <option value="Server">Server</option>
                </select>
              </div>
              <div class="form-group">
                <label for="manufacturer">Manufacturer:</label>
                <input type="text" id="manufacturer" name="manufacturer" placeholder="e.g., Dell, HP, Lenovo" required>
              </div>
              <div class="form-group">
                <label for="model">Model:</label>
                <input type="text" id="model" name="model" placeholder="e.g., XPS 15, ProBook 450 G7" required>
              </div>

              <div class="form-section">
                <h3>Specifications</h3>
                <div class="form-group">
                  <label for="processor">Processor:</label>
                  <input type="text" id="processor" name="specifications.processor" placeholder="e.g., Intel Core i7-10750H">
                </div>
                <div class="form-group">
                  <label for="memory">Memory (RAM):</label>
                  <input type="text" id="memory" name="specifications.memory" placeholder="e.g., 16GB DDR4">
                </div>
                <div class="form-group">
                  <label for="storage">Storage:</label>
                  <input type="text" id="storage" name="specifications.storage" placeholder="e.g., 512GB SSD">
                </div>
                <div class="form-group">
                  <label for="operatingSystem">Operating System:</label>
                  <input type="text" id="operatingSystem" name="specifications.operatingSystem" placeholder="e.g., Windows 11 Pro">
                </div>
              </div>

              <div class="form-group">
                <label for="status">Status:</label>
                <select id="status" name="status" required>
                  <option value="Available" selected>Available</option>
                  <option value="Assigned">Assigned</option>
                  <option value="Maintenance">Maintenance</option>
                  <option value="Retired">Retired</option>
                </select>
              </div>
              <div class="form-group">
                <label for="location">Location:</label>
                <input type="text" id="location" name="location" placeholder="e.g., Main Office, Floor 2" required>
              </div>
              <div class="form-group">
                <label for="assignedTo">Assigned To:</label>
                <input type="text" id="assignedTo" name="assignedTo" placeholder="e.g., John Smith">
              </div>
              <div class="form-group">
                <label for="serialNumber">Serial Number:</label>
                <input type="text" id="serialNumber" name="serialNumber" placeholder="e.g., SN12345678">
              </div>
              <div class="form-group">
                <label for="macAddress">MAC Address:</label>
                <input type="text" id="macAddress" name="macAddress" placeholder="e.g., 00:1A:2B:3C:4D:5E">
              </div>
              <div class="form-group">
                <label for="ipAddress">IP Address:</label>
                <input type="text" id="ipAddress" name="ipAddress" placeholder="e.g., *************">
              </div>
              <div class="form-group">
                <label for="purchaseDate">Purchase Date:</label>
                <input type="date" id="purchaseDate" name="purchaseDate" required>
              </div>
              <div class="form-group">
                <label for="purchasePrice">Purchase Price ($):</label>
                <input type="number" id="purchasePrice" name="purchasePrice" required>
              </div>
              <div class="form-group">
                <label for="warrantyExpiryDate">Warranty Expiry Date:</label>
                <input type="date" id="warrantyExpiryDate" name="warrantyExpiryDate">
              </div>
              <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('add-modal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addAsset()">Add Asset</button>
              </div>
            </form>
          </div>
        </div>
      `;

      // Add modal to the DOM
      document.body.insertAdjacentHTML('beforeend', modalHtml);

      // Show the modal
      document.getElementById('add-modal').style.display = 'flex';
    }

    async function addAsset() {
      try {
        // Get form data
        const form = document.getElementById('add-asset-form');
        const formData = {
          name: form.name.value,
          description: form.description.value,
          category: form.category.value,
          manufacturer: form.manufacturer.value,
          model: form.model.value,
          specifications: {
            processor: form.querySelector('#processor').value,
            memory: form.querySelector('#memory').value,
            storage: form.querySelector('#storage').value,
            operatingSystem: form.querySelector('#operatingSystem').value
          },
          status: form.status.value,
          location: form.location.value,
          assignedTo: form.assignedTo.value,
          serialNumber: form.serialNumber.value,
          macAddress: form.macAddress.value,
          ipAddress: form.ipAddress.value,
          purchaseDate: form.purchaseDate.value,
          purchasePrice: parseFloat(form.purchasePrice.value),
          warrantyExpiryDate: form.warrantyExpiryDate.value || null
        };

        // Send POST request to create the asset
        const response = await fetch('http://localhost:5000/api/assets', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
          alert('Asset added successfully!');
          closeModal('add-modal');

          // Refresh the asset list
          const assetListElement = document.getElementById('asset-list');
          assetListElement.innerHTML = '';
          document.getElementById('loading').style.display = 'block';
          await renderAssets();
        } else {
          alert(`Error: ${result.error}`);
        }
      } catch (error) {
        console.error('Error adding asset:', error);
        alert('Error adding new asset. Please try again.');
      }
    }

    // Global variable to store all assets
    let allAssets = [];

    // Search assets function
    function searchAssets() {
      const searchTerm = document.getElementById('search-input').value.toLowerCase();
      filterAndDisplayAssets(searchTerm);
    }

    // Filter assets function
    function filterAssets() {
      const searchTerm = document.getElementById('search-input').value.toLowerCase();
      filterAndDisplayAssets(searchTerm);
    }

    // Combined filter and display function
    function filterAndDisplayAssets(searchTerm) {
      const categoryFilter = document.getElementById('category-filter').value;
      const statusFilter = document.getElementById('status-filter').value;

      const assetListElement = document.getElementById('asset-list');
      assetListElement.innerHTML = '';

      const filteredAssets = allAssets.filter(asset => {
        // Search term filter
        const matchesSearch = searchTerm === '' ||
          asset.name.toLowerCase().includes(searchTerm) ||
          (asset.description && asset.description.toLowerCase().includes(searchTerm)) ||
          asset.category.toLowerCase().includes(searchTerm) ||
          asset.status.toLowerCase().includes(searchTerm) ||
          asset.location.toLowerCase().includes(searchTerm) ||
          (asset.assignedTo && asset.assignedTo.toLowerCase().includes(searchTerm)) ||
          (asset.serialNumber && asset.serialNumber.toLowerCase().includes(searchTerm));

        // Category filter
        const matchesCategory = categoryFilter === '' || asset.category === categoryFilter;

        // Status filter
        const matchesStatus = statusFilter === '' || asset.status === statusFilter;

        return matchesSearch && matchesCategory && matchesStatus;
      });

      if (filteredAssets.length === 0) {
        assetListElement.innerHTML = '<p class="no-results">No assets found matching your criteria.</p>';
        return;
      }

      // Render filtered assets
      filteredAssets.forEach(asset => {
        const assetCard = document.createElement('div');
        assetCard.className = 'asset-card';

        // Format the purchase date
        const purchaseDate = new Date(asset.purchaseDate).toLocaleDateString();

        assetCard.innerHTML = `
          <h3>${asset.name}</h3>
          <p><strong>Category:</strong> ${asset.category}</p>
          <p><strong>Status:</strong> ${asset.status}</p>
          <p><strong>Location:</strong> ${asset.location}</p>
          ${asset.assignedTo ? `<p><strong>Assigned To:</strong> ${asset.assignedTo}</p>` : ''}
          <p><strong>Purchase Date:</strong> ${purchaseDate}</p>
          <p><strong>Purchase Price:</strong> $${asset.purchasePrice}</p>
          <div class="asset-actions">
            <button class="btn btn-primary" onclick="viewAsset('${asset._id}')"><i class="fas fa-eye"></i> View</button>
            <button class="btn btn-secondary" onclick="editAsset('${asset._id}')"><i class="fas fa-edit"></i> Edit</button>
            <button class="btn btn-danger" onclick="deleteAsset('${asset._id}')"><i class="fas fa-trash"></i> Delete</button>
          </div>
        `;

        assetListElement.appendChild(assetCard);
      });
    }

    // Function to update dashboard
    function updateDashboard(assets) {
      // Update total assets count
      document.getElementById('total-assets-count').textContent = assets.length;

      // Count assets by category
      const laptopCount = assets.filter(asset => asset.category === 'Laptop').length;
      const desktopCount = assets.filter(asset => asset.category === 'Desktop').length;
      const serverCount = assets.filter(asset => asset.category === 'Server').length;

      // Update dashboard counts
      document.getElementById('laptop-assets-count').textContent = laptopCount;
      document.getElementById('desktop-assets-count').textContent = desktopCount;
      document.getElementById('server-assets-count').textContent = serverCount;
    }

    // Override the renderAssets function to store assets globally
    async function renderAssets() {
      const assetListElement = document.getElementById('asset-list');
      const loadingElement = document.getElementById('loading');

      try {
        // Fetch assets from the API
        allAssets = await fetchAssets();

        // Hide loading indicator
        loadingElement.style.display = 'none';

        if (allAssets.length === 0) {
          assetListElement.innerHTML = '<p>No assets found.</p>';
          return;
        }

        // Update dashboard
        updateDashboard(allAssets);

        // Apply any existing filters
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        filterAndDisplayAssets(searchTerm);

      } catch (error) {
        console.error('Error rendering assets:', error);
        loadingElement.style.display = 'none';
        assetListElement.innerHTML = '<p>Error loading assets. Please try again later.</p>';
      }
    }

    // Add event listener for search input
    document.addEventListener('DOMContentLoaded', () => {
      const searchInput = document.getElementById('search-input');
      searchInput.addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
          searchAssets();
        }
      });

      // Initialize the application
      renderAssets();
    });
  </script>
</body>
</html>
