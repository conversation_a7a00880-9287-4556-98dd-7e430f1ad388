const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const User = require('../src/models/User');
const Asset = require('../src/models/Asset');

// Import all routes
const authRoutes = require('../src/routes/auth');
const userRoutes = require('../src/routes/users');
const assetRoutes = require('../src/routes/assets');

// Create test app
const app = express();
app.use(express.json());
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/assets', assetRoutes);

describe('Integration Tests - Complete Asset Management Flow', () => {
  let adminToken, userToken;
  let testAsset;

  beforeAll(async () => {
    // Create super admin
    const admin = await User.create({
      username: 'admin',
      password: 'admin123',
      role: 'superAdmin'
    });

    // Create regular user
    const user = await User.create({
      username: 'user',
      password: 'user123',
      role: 'user'
    });

    // Login as admin
    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({ username: 'admin', password: 'admin123' });
    adminToken = adminLogin.body.token;

    // Login as user
    const userLogin = await request(app)
      .post('/api/auth/login')
      .send({ username: 'user', password: 'user123' });
    userToken = userLogin.body.token;
  });

  describe('Asset Management Workflow', () => {
    it('should complete full asset lifecycle', async () => {
      // 1. Create asset (admin only)
      const createResponse = await request(app)
        .post('/api/assets')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          assetTag: 'TEST001',
          status: 'Available',
          manufacturer: 'Test Corp',
          model: 'Test Model',
          assetType: 'Laptop',
          serialNumber: 'TST123456',
          currentOwner: '',
          remarks: 'Test asset for integration testing'
        });

      expect(createResponse.status).toBe(201);
      expect(createResponse.body.success).toBe(true);
      expect(createResponse.body.data.assetTag).toBe('TEST001');
      testAsset = createResponse.body.data;

      // 2. Get all assets (any authenticated user)
      const getAllResponse = await request(app)
        .get('/api/assets')
        .set('Authorization', `Bearer ${userToken}`);

      expect(getAllResponse.status).toBe(200);
      expect(getAllResponse.body.success).toBe(true);
      expect(getAllResponse.body.data.length).toBeGreaterThan(0);

      // 3. Get specific asset (any authenticated user)
      const getOneResponse = await request(app)
        .get(`/api/assets/${testAsset._id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(getOneResponse.status).toBe(200);
      expect(getOneResponse.body.success).toBe(true);
      expect(getOneResponse.body.data.assetTag).toBe('TEST001');

      // 4. Update asset (admin only)
      const updateResponse = await request(app)
        .put(`/api/assets/${testAsset._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          status: 'Assigned',
          currentOwner: 'John Doe',
          remarks: 'Updated test asset'
        });

      expect(updateResponse.status).toBe(200);
      expect(updateResponse.body.success).toBe(true);
      expect(updateResponse.body.data.status).toBe('Assigned');
      expect(updateResponse.body.data.currentOwner).toBe('John Doe');

      // 5. Get asset history (authenticated user)
      const historyResponse = await request(app)
        .get(`/api/assets/${testAsset._id}/history`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(historyResponse.status).toBe(200);
      expect(historyResponse.body.success).toBe(true);
      expect(historyResponse.body.history.length).toBeGreaterThan(0);

      // 6. Delete asset (admin only)
      const deleteResponse = await request(app)
        .delete(`/api/assets/${testAsset._id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(deleteResponse.status).toBe(200);
      expect(deleteResponse.body.success).toBe(true);

      // 7. Verify asset is deleted
      const verifyDeleteResponse = await request(app)
        .get(`/api/assets/${testAsset._id}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(verifyDeleteResponse.status).toBe(404);
    });

    it('should enforce role-based permissions', async () => {
      // User cannot create assets
      const createResponse = await request(app)
        .post('/api/assets')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          assetTag: 'FORBIDDEN001',
          status: 'Available',
          manufacturer: 'Test Corp',
          model: 'Test Model',
          assetType: 'Laptop',
          serialNumber: 'FORBIDDEN123'
        });

      expect(createResponse.status).toBe(403);
      expect(createResponse.body.success).toBe(false);
    });

    it('should validate asset data', async () => {
      // Missing required fields
      const invalidResponse = await request(app)
        .post('/api/assets')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          assetTag: '',  // Required field missing
          status: 'Available'
        });

      expect(invalidResponse.status).toBe(400);
      expect(invalidResponse.body.success).toBe(false);
    });

    it('should prevent duplicate asset tags', async () => {
      // Create first asset
      await request(app)
        .post('/api/assets')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          assetTag: 'DUPLICATE001',
          status: 'Available',
          manufacturer: 'Test Corp',
          model: 'Test Model',
          assetType: 'Laptop',
          serialNumber: 'DUP123456'
        });

      // Try to create duplicate
      const duplicateResponse = await request(app)
        .post('/api/assets')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          assetTag: 'DUPLICATE001',  // Same asset tag
          status: 'Available',
          manufacturer: 'Test Corp',
          model: 'Test Model',
          assetType: 'Laptop',
          serialNumber: 'DUP789012'  // Different serial
        });

      expect(duplicateResponse.status).toBe(400);
      expect(duplicateResponse.body.success).toBe(false);
    });
  });

  describe('Authentication Flow', () => {
    it('should handle complete authentication workflow', async () => {
      // 1. Create new user (admin only)
      const createUserResponse = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'newuser',
          password: 'newpass123'
        });

      expect(createUserResponse.status).toBe(201);
      expect(createUserResponse.body.success).toBe(true);

      // 2. Login with new user
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'newuser',
          password: 'newpass123'
        });

      expect(loginResponse.status).toBe(200);
      expect(loginResponse.body.success).toBe(true);
      expect(loginResponse.body.token).toBeDefined();
      expect(loginResponse.body.user.role).toBe('user');

      // 3. Access protected route with new token
      const protectedResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${loginResponse.body.token}`);

      expect(protectedResponse.status).toBe(200);
      expect(protectedResponse.body.success).toBe(true);
      expect(protectedResponse.body.data.username).toBe('newuser');
    });
  });
});
