const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const { protect, authorize } = require('../middleware/auth');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5000000 // 5MB default
  },
  fileFilter: function (req, file, cb) {
    if (file.mimetype === 'text/csv' || file.mimetype === 'application/vnd.ms-excel') {
      cb(null, true);
    } else {
      cb(new Error('Only CSV files are allowed!'), false);
    }
  }
});

const {
  getAssets,
  getAsset,
  createAsset,
  updateAsset,
  deleteAsset,
  importAssetsFromCSV,
  getAssetHistory
} = require('../controllers/assetController');

// Public routes (require authentication but any role)
router.get('/', protect, getAssets);
router.get('/:id', protect, getAsset);
router.get('/:id/history', protect, getAssetHistory);

// Admin only routes
router.post('/', protect, authorize('superAdmin'), createAsset);
router.put('/:id', protect, authorize('superAdmin'), updateAsset);
router.delete('/:id', protect, authorize('superAdmin'), deleteAsset);
router.post('/import', protect, authorize('superAdmin'), upload.single('file'), importAssetsFromCSV);

module.exports = router;
