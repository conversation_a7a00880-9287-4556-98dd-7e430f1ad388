const express = require('express');
const router = express.Router();
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });
const {
  getAssets,
  getAsset,
  createAsset,
  updateAsset,
  deleteAsset,
  importAssetsFromCSV,
  getAssetHistory
} = require('../controllers/assetController');

router
  .route('/')
  .get(getAssets)
  .post(createAsset);

router
  .route('/:id')
  .get(getAsset)
  .put(updateAsset)
  .delete(deleteAsset);

router
  .route('/import')
  .post(upload.single('file'), importAssetsFromCSV);

router
  .route('/:id/history')
  .get(getAssetHistory);

module.exports = router;
