Write-Host "Testing Frontend..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000"
    Write-Host "SUCCESS: Frontend is accessible!" -ForegroundColor Green
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Cyan
    Write-Host "Content Length: $($response.Content.Length) bytes" -ForegroundColor Cyan
    
    if ($response.Content -like "*Asset Management*") {
        Write-Host "SUCCESS: Frontend contains expected content!" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Frontend content may not be correct" -ForegroundColor Yellow
    }
} catch {
    Write-Host "ERROR: Frontend not accessible - $($_.Exception.Message)" -ForegroundColor Red
}
