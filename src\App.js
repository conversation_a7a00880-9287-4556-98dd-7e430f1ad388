import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [assets, setAssets] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate fetching data from the backend
    setTimeout(() => {
      setAssets([
        {
          id: 1,
          name: 'Sample Laptop',
          category: 'Hardware',
          status: 'Assigned',
          location: 'Office 101',
          assignedTo: '<PERSON>',
          purchaseDate: '2023-01-15',
          purchasePrice: 1200
        },
        {
          id: 2,
          name: 'Office Chair',
          category: 'Furniture',
          status: 'Available',
          location: 'Storage Room',
          assignedTo: '',
          purchaseDate: '2022-11-20',
          purchasePrice: 250
        },
        {
          id: 3,
          name: 'Microsoft Office License',
          category: 'Software',
          status: 'Assigned',
          location: 'N/A',
          assignedTo: '<PERSON> Smith',
          purchaseDate: '2023-03-05',
          purchasePrice: 150
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  return (
    <div className="app">
      <header className="app-header">
        <h1>Asset Management System</h1>
      </header>
      <main className="app-main">
        <h2>Asset List</h2>
        {loading ? (
          <p>Loading assets...</p>
        ) : (
          <div className="asset-list">
            {assets.map(asset => (
              <div key={asset.id} className="asset-card">
                <h3>{asset.name}</h3>
                <p><strong>Category:</strong> {asset.category}</p>
                <p><strong>Status:</strong> {asset.status}</p>
                <p><strong>Location:</strong> {asset.location}</p>
                {asset.assignedTo && (
                  <p><strong>Assigned To:</strong> {asset.assignedTo}</p>
                )}
                <p><strong>Purchase Date:</strong> {asset.purchaseDate}</p>
                <p><strong>Purchase Price:</strong> ${asset.purchasePrice}</p>
                <div className="asset-actions">
                  <button className="btn btn-primary">View</button>
                  <button className="btn btn-secondary">Edit</button>
                  <button className="btn btn-danger">Delete</button>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>
      <footer className="app-footer">
        <p>&copy; 2025 Asset Management System</p>
      </footer>
    </div>
  );
}

export default App;
