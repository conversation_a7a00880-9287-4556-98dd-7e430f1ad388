import { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import axios from 'axios'

const AssetForm = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const isEditMode = !!id

  const [formData, setFormData] = useState({
    assetTag: '',
    status: 'Available',
    manufacturer: '',
    model: '',
    assetType: 'Desktop',
    serialNumber: '',
    currentOwner: '',
    remarks: ''
  })

  const [loading, setLoading] = useState(isEditMode)
  const [error, setError] = useState(null)
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    const fetchAsset = async () => {
      if (isEditMode) {
        try {
          const token = localStorage.getItem('token')
          const response = await axios.get(`/api/assets/${id}`, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          })
          const asset = response.data.data

          setFormData({
            assetTag: asset.assetTag || '',
            status: asset.status || 'Available',
            manufacturer: asset.manufacturer || '',
            model: asset.model || '',
            assetType: asset.assetType || 'Desktop',
            serialNumber: asset.serialNumber || '',
            currentOwner: asset.currentOwner || '',
            remarks: asset.remarks || ''
          })
          setLoading(false)
        } catch (error) {
          setError('Error fetching asset details. Please try again later.')
          setLoading(false)
          console.error('Error fetching asset:', error)
        }
      }
    }

    fetchAsset()
  }, [id, isEditMode])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setSubmitting(true)
    setError(null)

    try {
      const token = localStorage.getItem('token')
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }

      if (isEditMode) {
        await axios.put(`/api/assets/${id}`, formData, config)
      } else {
        await axios.post('/api/assets', formData, config)
      }
      navigate('/assets')
    } catch (error) {
      console.error('Error saving asset:', error)
      if (error.response?.data?.error) {
        setError(Array.isArray(error.response.data.error)
          ? error.response.data.error.join(', ')
          : error.response.data.error)
      } else {
        setError('Error saving asset. Please check your inputs and try again.')
      }
      setSubmitting(false)
    }
  }

  if (loading) return <div>Loading...</div>

  return (
    <div>
      <h1>{isEditMode ? 'Edit Asset' : 'Add New Asset'}</h1>

      {error && <div className="error">{error}</div>}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="assetTag">Asset Tag *</label>
          <input
            type="text"
            id="assetTag"
            name="assetTag"
            value={formData.assetTag}
            onChange={handleChange}
            className="form-control"
            required
            placeholder="e.g., LAPTOP001"
          />
        </div>

        <div className="form-group">
          <label htmlFor="manufacturer">Manufacturer *</label>
          <input
            type="text"
            id="manufacturer"
            name="manufacturer"
            value={formData.manufacturer}
            onChange={handleChange}
            className="form-control"
            required
            placeholder="e.g., Dell, HP, Apple"
          />
        </div>

        <div className="form-group">
          <label htmlFor="model">Model *</label>
          <input
            type="text"
            id="model"
            name="model"
            value={formData.model}
            onChange={handleChange}
            className="form-control"
            required
            placeholder="e.g., Latitude 5520, MacBook Pro"
          />
        </div>

        <div className="form-group">
          <label htmlFor="assetType">Asset Type *</label>
          <select
            id="assetType"
            name="assetType"
            value={formData.assetType}
            onChange={handleChange}
            className="form-control"
            required
          >
            <option value="Desktop">Desktop</option>
            <option value="Laptop">Laptop</option>
            <option value="Server">Server</option>
            <option value="Mobile">Mobile</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="serialNumber">Serial Number *</label>
          <input
            type="text"
            id="serialNumber"
            name="serialNumber"
            value={formData.serialNumber}
            onChange={handleChange}
            className="form-control"
            required
            placeholder="Device serial number"
          />
        </div>

        <div className="form-group">
          <label htmlFor="status">Status *</label>
          <select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            className="form-control"
            required
          >
            <option value="Available">Available</option>
            <option value="Assigned">Assigned</option>
            <option value="Maintenance">Maintenance</option>
            <option value="Retired">Retired</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="currentOwner">Current Owner</label>
          <input
            type="text"
            id="currentOwner"
            name="currentOwner"
            value={formData.currentOwner}
            onChange={handleChange}
            className="form-control"
            placeholder="Employee name or department"
          />
        </div>

        <div className="form-group">
          <label htmlFor="remarks">Remarks</label>
          <textarea
            id="remarks"
            name="remarks"
            value={formData.remarks}
            onChange={handleChange}
            className="form-control"
            rows="3"
            placeholder="Additional notes or comments"
          />
        </div>

        <div className="form-actions">
          <button
            type="submit"
            className="btn btn-primary"
            disabled={submitting}
          >
            {submitting ? 'Saving...' : 'Save Asset'}
          </button>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={() => navigate('/assets')}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  )
}

export default AssetForm
