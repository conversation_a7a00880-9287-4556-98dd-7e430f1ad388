// MongoDB initialization script
db = db.getSiblingDB('asset-management');

// Create collections
db.createCollection('users');
db.createCollection('assets');

// Create indexes for better performance
db.users.createIndex({ "username": 1 }, { unique: true });
db.assets.createIndex({ "assetTag": 1 }, { unique: true });
db.assets.createIndex({ "serialNumber": 1 }, { unique: true });
db.assets.createIndex({ "status": 1 });
db.assets.createIndex({ "assetType": 1 });
db.assets.createIndex({ "manufacturer": 1 });

// Create default super admin user
// Password: admin123 (hashed with bcrypt)
db.users.insertOne({
  username: "admin",
  password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // admin123
  role: "superAdmin",
  createdAt: new Date(),
  updatedAt: new Date()
});

// Create sample assets for testing
db.assets.insertMany([
  {
    assetTag: "LAPTOP001",
    status: "Available",
    manufacturer: "Dell",
    model: "Latitude 5520",
    assetType: "Laptop",
    serialNumber: "DL123456789",
    currentOwner: "",
    remarks: "Brand new laptop for development team",
    createdAt: new Date(),
    updatedAt: new Date(),
    history: [{
      changedBy: "admin",
      changeType: "created",
      timestamp: new Date(),
      snapshot: {
        assetTag: "LAPTOP001",
        status: "Available",
        manufacturer: "Dell",
        model: "Latitude 5520",
        assetType: "Laptop",
        serialNumber: "DL123456789",
        currentOwner: "",
        remarks: "Brand new laptop for development team"
      }
    }]
  },
  {
    assetTag: "DESKTOP001",
    status: "Assigned",
    manufacturer: "HP",
    model: "EliteDesk 800",
    assetType: "Desktop",
    serialNumber: "HP987654321",
    currentOwner: "John Smith",
    remarks: "Assigned to accounting department",
    createdAt: new Date(),
    updatedAt: new Date(),
    history: [{
      changedBy: "admin",
      changeType: "created",
      timestamp: new Date(),
      snapshot: {
        assetTag: "DESKTOP001",
        status: "Assigned",
        manufacturer: "HP",
        model: "EliteDesk 800",
        assetType: "Desktop",
        serialNumber: "HP987654321",
        currentOwner: "John Smith",
        remarks: "Assigned to accounting department"
      }
    }]
  },
  {
    assetTag: "SERVER001",
    status: "Maintenance",
    manufacturer: "Dell",
    model: "PowerEdge R740",
    assetType: "Server",
    serialNumber: "SRV123456789",
    currentOwner: "IT Department",
    remarks: "Database server - scheduled maintenance",
    createdAt: new Date(),
    updatedAt: new Date(),
    history: [{
      changedBy: "admin",
      changeType: "created",
      timestamp: new Date(),
      snapshot: {
        assetTag: "SERVER001",
        status: "Maintenance",
        manufacturer: "Dell",
        model: "PowerEdge R740",
        assetType: "Server",
        serialNumber: "SRV123456789",
        currentOwner: "IT Department",
        remarks: "Database server - scheduled maintenance"
      }
    }]
  }
]);

print("Database initialized successfully with sample data!");
print("Default admin user created - username: admin, password: admin123");
print("Sample assets created for testing purposes");
