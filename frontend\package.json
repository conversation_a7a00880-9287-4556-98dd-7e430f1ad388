{"name": "asset-management-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"axios": "^1.3.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "jwt-decode": "^3.1.2", "papaparse": "^5.4.1"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "vite": "^4.2.0", "vitest": "^0.30.1", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.4.3", "jsdom": "^22.0.0"}}