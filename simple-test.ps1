# Simple Test Script for Asset Management Application
Write-Host "🧪 Asset Management Application - Simple Test" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Function to test HTTP endpoint
function Test-Endpoint {
    param([string]$Url, [string]$Name)
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $Name - OK" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Name - Status: $($response.StatusCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ $Name - Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test 1: Check if Dock<PERSON> is running
Write-Host "`n📋 Step 1: Checking Docker..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker is available: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not available" -ForegroundColor Red
    exit 1
}

# Test 2: Start MongoDB only first
Write-Host "`n📋 Step 2: Starting MongoDB..." -ForegroundColor Yellow
try {
    docker run -d --name test-mongo -p 27017:27017 mongo:6.0
    Start-Sleep -Seconds 10
    Write-Host "✅ MongoDB started" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start MongoDB" -ForegroundColor Red
}

# Test 3: Test backend locally
Write-Host "`n📋 Step 3: Testing Backend Locally..." -ForegroundColor Yellow
$backendProcess = $null
try {
    # Start backend in background
    $backendProcess = Start-Process -FilePath "node" -ArgumentList "backend/src/index.js" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 5
    
    # Test backend health
    $healthOk = Test-Endpoint -Url "http://localhost:5000/health" -Name "Backend Health"
    
    if ($healthOk) {
        # Test login
        try {
            $loginBody = @{
                username = "admin"
                password = "admin123"
            } | ConvertTo-Json
            
            $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
            
            if ($loginResponse.success) {
                Write-Host "✅ Admin login works" -ForegroundColor Green
                $token = $loginResponse.token
                
                # Test protected endpoint
                $headers = @{ "Authorization" = "Bearer $token" }
                $assetsResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/assets" -Method GET -Headers $headers
                
                if ($assetsResponse.success) {
                    Write-Host "✅ Protected API works - Found $($assetsResponse.data.Count) assets" -ForegroundColor Green
                } else {
                    Write-Host "❌ Protected API failed" -ForegroundColor Red
                }
            } else {
                Write-Host "❌ Admin login failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ API test failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "❌ Backend test failed: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($backendProcess) {
        Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
    }
}

# Test 4: Test frontend build
Write-Host "`n📋 Step 4: Testing Frontend Build..." -ForegroundColor Yellow
try {
    Set-Location frontend
    $buildResult = npm run build
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Frontend builds successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Frontend build failed" -ForegroundColor Red
    }
    Set-Location ..
} catch {
    Write-Host "❌ Frontend build test failed: $($_.Exception.Message)" -ForegroundColor Red
    Set-Location ..
}

# Test 5: Test Docker Compose (simplified)
Write-Host "`n📋 Step 5: Testing Docker Compose..." -ForegroundColor Yellow
try {
    # Clean up first
    docker stop test-mongo -ErrorAction SilentlyContinue
    docker rm test-mongo -ErrorAction SilentlyContinue
    
    # Try to build images
    Write-Host "Building Docker images..." -ForegroundColor Cyan
    $buildResult = docker-compose build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker images built successfully" -ForegroundColor Green
        
        # Try to start services
        Write-Host "Starting services..." -ForegroundColor Cyan
        docker-compose up -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Services started" -ForegroundColor Green
            Start-Sleep -Seconds 30
            
            # Test services
            $frontendOk = Test-Endpoint -Url "http://localhost:3000" -Name "Frontend"
            $backendOk = Test-Endpoint -Url "http://localhost:5000/health" -Name "Backend"
            
            if ($frontendOk -and $backendOk) {
                Write-Host "`n🎉 ALL TESTS PASSED!" -ForegroundColor Green
                Write-Host "Application is working correctly!" -ForegroundColor Green
                Write-Host "`nAccess URLs:" -ForegroundColor Cyan
                Write-Host "Frontend: http://localhost:3000" -ForegroundColor White
                Write-Host "Backend: http://localhost:5000" -ForegroundColor White
                Write-Host "`nDefault Admin:" -ForegroundColor Yellow
                Write-Host "Username: admin" -ForegroundColor White
                Write-Host "Password: admin123" -ForegroundColor White
            } else {
                Write-Host "`n⚠️  Services started but not responding correctly" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ Failed to start services" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Docker build failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Docker test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📊 Test Summary Complete" -ForegroundColor Green
Write-Host "Check the results above to see what's working." -ForegroundColor White
Write-Host "If all tests passed, your application is production-ready!" -ForegroundColor Green
