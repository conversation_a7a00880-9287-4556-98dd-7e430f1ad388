import { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import axios from 'axios'

const AssetDetail = () => {
  const { id } = useParams()
  const [asset, setAsset] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchAsset = async () => {
      try {
        const token = localStorage.getItem('token')
        const response = await axios.get(`/api/assets/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })
        setAsset(response.data.data)
        setLoading(false)
      } catch (error) {
        console.error('Error fetching asset:', error)
        if (error.response?.status === 401) {
          setError('Please login to view asset details.')
          window.location.href = '/login'
        } else if (error.response?.status === 404) {
          setError('Asset not found.')
        } else {
          setError('Error fetching asset details. Please try again later.')
        }
        setLoading(false)
      }
    }
    fetchAsset()
  }, [id])

  if (loading) return <div>Loading asset details...</div>
  if (error) return <div className="error">{error}</div>
  if (!asset) return <div>Asset not found.</div>

  return (
    <div className="asset-detail">
      <h1>Asset Details</h1>
      <div><strong>Asset Tag:</strong> {asset.assetTag}</div>
      <div><strong>Status:</strong> {asset.status}</div>
      <div><strong>Manufacturer:</strong> {asset.manufacturer}</div>
      <div><strong>Model:</strong> {asset.model}</div>
      <div><strong>Asset Type:</strong> {asset.assetType}</div>
      <div><strong>Serial Number:</strong> {asset.serialNumber}</div>
      <div><strong>Current Owner:</strong> {asset.currentOwner}</div>
      <div><strong>Remarks:</strong> {asset.remarks}</div>
    </div>
  )
}

export default AssetDetail
