import { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import axios from 'axios'

const AssetDetail = () => {
  const { id } = useParams()
  const [asset, setAsset] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchAsset = async () => {
      try {
        const token = localStorage.getItem('token')
        const response = await axios.get(`/api/assets/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })
        setAsset(response.data.data)
        setLoading(false)
      } catch (error) {
        console.error('Error fetching asset:', error)
        if (error.response?.status === 401) {
          setError('Please login to view asset details.')
          window.location.href = '/login'
        } else if (error.response?.status === 404) {
          setError('Asset not found.')
        } else {
          setError('Error fetching asset details. Please try again later.')
        }
        setLoading(false)
      }
    }
    fetchAsset()
  }, [id])

  if (loading) return <div>Loading asset details...</div>
  if (error) return <div className="error">{error}</div>
  if (!asset) return <div>Asset not found.</div>

  return (
    <div className="asset-detail">
      <div className="asset-header">
        <h1>Asset Details</h1>
        <div className="asset-actions">
          <button
            className="btn btn-secondary"
            onClick={() => window.history.back()}
          >
            Back
          </button>
        </div>
      </div>

      <div className="asset-info">
        <div className="info-section">
          <h3>Basic Information</h3>
          <div className="info-grid">
            <div className="info-item">
              <strong>Asset Tag:</strong>
              <span>{asset.assetTag}</span>
            </div>
            <div className="info-item">
              <strong>Status:</strong>
              <span className={`status-badge status-${asset.status.toLowerCase()}`}>
                {asset.status}
              </span>
            </div>
            <div className="info-item">
              <strong>Asset Type:</strong>
              <span>{asset.assetType}</span>
            </div>
            <div className="info-item">
              <strong>Serial Number:</strong>
              <span>{asset.serialNumber}</span>
            </div>
          </div>
        </div>

        <div className="info-section">
          <h3>Device Information</h3>
          <div className="info-grid">
            <div className="info-item">
              <strong>Manufacturer:</strong>
              <span>{asset.manufacturer}</span>
            </div>
            <div className="info-item">
              <strong>Model:</strong>
              <span>{asset.model}</span>
            </div>
          </div>
        </div>

        <div className="info-section">
          <h3>Assignment Information</h3>
          <div className="info-grid">
            <div className="info-item">
              <strong>Current Owner:</strong>
              <span>{asset.currentOwner || 'Unassigned'}</span>
            </div>
            <div className="info-item">
              <strong>Remarks:</strong>
              <span>{asset.remarks || 'No remarks'}</span>
            </div>
          </div>
        </div>

        <div className="info-section">
          <h3>Timestamps</h3>
          <div className="info-grid">
            <div className="info-item">
              <strong>Created:</strong>
              <span>{new Date(asset.createdAt).toLocaleDateString()}</span>
            </div>
            <div className="info-item">
              <strong>Last Updated:</strong>
              <span>{new Date(asset.updatedAt).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AssetDetail
