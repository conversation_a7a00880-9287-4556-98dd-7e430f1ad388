import '@testing-library/jest-dom'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock

// Mock window.location
delete window.location
window.location = { href: '', assign: vi.fn() }

// Mock jwt-decode
vi.mock('jwt-decode', () => ({
  default: vi.fn(() => ({
    id: 'test-user-id',
    role: 'user',
    username: 'testuser'
  }))
}))

// Mock axios
vi.mock('axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  }
}))
