import { Link, useNavigate } from 'react-router-dom'
import { useEffect, useState } from 'react'
import jwtDecode from 'jwt-decode'
import './Navbar.css'

const Navbar = () => {
  const [userRole, setUserRole] = useState(null)
  const navigate = useNavigate()

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      const decoded = jwtDecode(token)
      setUserRole(decoded.role)
    } else {
      setUserRole(null)
    }
  }, [])

  const handleLogout = () => {
    localStorage.removeItem('token')
    setUserRole(null)
    navigate('/login')
  }

  return (
    <nav className="navbar">
      <div className="container">
        <Link to="/" className="navbar-brand">Asset Management</Link>
        <ul className="navbar-nav">
          <li className="nav-item">
            <Link to="/" className="nav-link">Home</Link>
          </li>
          <li className="nav-item">
            <Link to="/assets" className="nav-link">Assets</Link>
          </li>
          {userRole === 'superAdmin' && (
            <li className="nav-item">
              <Link to="/assets/new" className="nav-link">Add Asset</Link>
            </li>
          )}
          {userRole ? (
            <>
              <li className="nav-item"><span className="nav-link">Role: {userRole}</span></li>
              <li className="nav-item"><button className="nav-link" onClick={handleLogout}>Logout</button></li>
            </>
          ) : (
            <li className="nav-item"><Link to="/login" className="nav-link">Login</Link></li>
          )}
        </ul>
      </div>
    </nav>
  )
}

export default Navbar
