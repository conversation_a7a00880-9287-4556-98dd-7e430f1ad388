import { Link } from 'react-router-dom'
import { useEffect, useState } from 'react'
import jwtDecode from 'jwt-decode'

const Home = () => {
  const [userRole, setUserRole] = useState(null)

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      const decoded = jwtDecode(token)
      setUserRole(decoded.role)
    } else {
      setUserRole(null)
    }
  }, [])

  return (
    <div className="home">
      <h1>Welcome to Asset Management</h1>
      {userRole ? (
        <p>You are logged in as <strong>{userRole}</strong>.</p>
      ) : (
        <p>Please <a href="/login">login</a> to manage assets.</p>
      )}
      
      <div className="features">
        <div className="feature">
          <h2>Track Assets</h2>
          <p>Keep track of all your organization's assets in one place.</p>
        </div>
        <div className="feature">
          <h2>Manage Assignments</h2>
          <p>Assign assets to employees and track who is using what.</p>
        </div>
        <div className="feature">
          <h2>Monitor Status</h2>
          <p>Keep track of asset status, maintenance, and retirement.</p>
        </div>
      </div>
      
      <div className="cta">
        <Link to="/assets" className="btn btn-primary">View Assets</Link>
        <Link to="/assets/new" className="btn btn-secondary">Add New Asset</Link>
      </div>
    </div>
  )
}

export default Home
