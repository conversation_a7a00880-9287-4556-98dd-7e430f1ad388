import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import axios from 'axios'
import AssetCard from '../components/AssetCard'
import jwtDecode from 'jwt-decode'

const AssetList = () => {
  const [assets, setAssets] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [isSuperAdmin, setIsSuperAdmin] = useState(false)

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      const decoded = jwtDecode(token)
      if (decoded && decoded.role === 'superAdmin') {
        setIsSuperAdmin(true)
      } else {
        setIsSuperAdmin(false)
      }
    } else {
      setIsSuperAdmin(false)
    }
  }, [])

  const fetchAssets = async () => {
    try {
      const response = await axios.get('/api/assets')
      setAssets(response.data.data)
      setLoading(false)
    } catch (error) {
      setError('Error fetching assets. Please try again later.')
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAssets()
  }, [])

  const handleCSVImport = async (e) => {
    const file = e.target.files[0]
    if (!file) return
    const formData = new FormData()
    formData.append('file', file)
    const token = localStorage.getItem('token')
    try {
      await axios.post('/api/assets/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${token}`
        }
      })
      fetchAssets()
    } catch (err) {
      setError('Error importing CSV. Please check your file and try again.')
    }
  }

  if (loading) return <div>Loading assets...</div>
  if (error) return <div className="error">{error}</div>

  return (
    <div>
      <div className="list-header">
        <h1>Asset List</h1>
        {isSuperAdmin && (
          <>
            <Link to="/assets/new" className="btn btn-primary">Add New Asset</Link>
            <input type="file" accept=".csv" onChange={handleCSVImport} style={{ marginLeft: 16 }} />
          </>
        )}
      </div>
      {assets.length === 0 ? (
        <div className="no-assets">
          <p>No assets found. Start by adding a new asset.</p>
          {isSuperAdmin && <Link to="/assets/new" className="btn btn-primary">Add New Asset</Link>}
        </div>
      ) : (
        <div className="asset-list">
          {assets.map(asset => (
            <div key={asset._id} className="asset-card">
              <div><strong>Asset Tag:</strong> {asset.assetTag}</div>
              <div><strong>Status:</strong> {asset.status}</div>
              <div><strong>Manufacturer:</strong> {asset.manufacturer}</div>
              <div><strong>Model:</strong> {asset.model}</div>
              <div><strong>Asset Type:</strong> {asset.assetType}</div>
              <div><strong>Serial Number:</strong> {asset.serialNumber}</div>
              <div><strong>Current Owner:</strong> {asset.currentOwner}</div>
              <div><strong>Remarks:</strong> {asset.remarks}</div>
              <Link to={`/assets/${asset._id}`}>View</Link>
              {isSuperAdmin && <Link to={`/assets/edit/${asset._id}`}>Edit</Link>}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default AssetList
