# Asset Management Application

A simple asset management web application built with React and Node.js.

## Project Structure

- `/frontend` - React frontend application built with Vite
- `/backend` - Node.js backend API built with Express
- `/docker` - Docker configuration files

## Technologies Used

- Frontend: React, Vite
- Backend: Node.js, Express
- Database: MongoDB
- Containerization: Docker

## Getting Started

### Development Setup

1. Clone the repository
2. Install dependencies for both frontend and backend:
   ```
   cd frontend && npm install
   cd backend && npm install
   ```
3. Start the development servers:
   ```
   # Terminal 1
   cd frontend && npm run dev
   
   # Terminal 2
   cd backend && npm run dev
   ```

### Docker Setup

1. Make sure Docker and Docker Compose are installed
2. Run the application using Docker Compose:
   ```
   docker-compose up
   ```

## API Endpoints

- `GET /api/assets` - Get all assets
- `GET /api/assets/:id` - Get a specific asset
- `POST /api/assets` - Create a new asset
- `PUT /api/assets/:id` - Update an asset
- `DELETE /api/assets/:id` - Delete an asset
