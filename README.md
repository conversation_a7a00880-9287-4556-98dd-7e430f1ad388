# Asset Management Application

A comprehensive, production-ready asset management web application built with React and Node.js, featuring role-based access control, CSV import/export, and complete Docker deployment.

## 🚀 Features

- **Asset Management**: Complete CRUD operations for IT assets
- **Role-Based Access**: Super Admin and User roles with different permissions
- **Authentication**: JWT-based secure authentication system
- **CSV Import/Export**: Bulk asset management capabilities
- **Asset History**: Track all changes made to assets
- **Responsive Design**: Mobile-friendly interface
- **Production Ready**: Docker containerization with security best practices
- **Comprehensive Testing**: Unit and integration tests for both frontend and backend

## 🏗️ Project Structure

```
Asset-Management/
├── backend/                 # Node.js/Express API
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── middleware/      # Authentication & validation
│   │   ├── models/          # MongoDB models
│   │   └── routes/          # API routes
│   ├── tests/               # Backend tests
│   └── uploads/             # File upload directory
├── frontend/                # React application
│   ├── src/
│   │   ├── components/      # Reusable components
│   │   ├── pages/           # Page components
│   │   └── test/            # Frontend tests
├── docker/                  # Docker configuration
│   ├── Dockerfile.backend
│   ├── Dockerfile.frontend
│   ├── nginx.conf
│   └── mongo-init.js
├── scripts/                 # Deployment & maintenance scripts
└── docker-compose.yml       # Development environment
```

## 🛠️ Technologies Used

### Backend
- **Node.js** with Express.js framework
- **MongoDB** with Mongoose ODM
- **JWT** for authentication
- **Bcrypt** for password hashing
- **Multer** for file uploads
- **Helmet** for security headers
- **Rate limiting** for API protection
- **Jest** for testing

### Frontend
- **React 18** with modern hooks
- **Vite** for fast development and building
- **React Router** for navigation
- **Axios** for API communication
- **JWT-decode** for token handling
- **PapaParse** for CSV processing
- **Vitest** for testing

### DevOps & Deployment
- **Docker** & **Docker Compose** for containerization
- **Nginx** for production web server
- **MongoDB** with initialization scripts
- **Health checks** and **restart policies**
- **Volume management** for data persistence

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- Node.js 18+ (for local development)
- Git

### Option 1: Docker Deployment (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Asset-Management
   ```

2. **Start the application**
   ```bash
   # For development
   docker-compose up --build

   # For production (Linux/Mac)
   ./scripts/deploy.sh production

   # For production (Windows)
   docker-compose -f docker-compose.prod.yml up --build -d
   ```

3. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000
   - Default admin credentials: `admin` / `admin123`

### Option 2: Local Development

1. **Backend Setup**
   ```bash
   cd backend
   npm install
   cp .env.example .env
   # Edit .env with your MongoDB connection string
   npm run dev
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

3. **Database Setup**
   - Install MongoDB locally or use MongoDB Atlas
   - Update the connection string in backend/.env

## 🔐 Default Credentials

**Super Admin Account:**
- Username: `admin`
- Password: `admin123`

⚠️ **IMPORTANT**: Change the default password immediately after first login!

## 📋 API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user info

### Asset Endpoints
- `GET /api/assets` - Get all assets (requires authentication)
- `GET /api/assets/:id` - Get specific asset
- `POST /api/assets` - Create new asset (Super Admin only)
- `PUT /api/assets/:id` - Update asset (Super Admin only)
- `DELETE /api/assets/:id` - Delete asset (Super Admin only)
- `POST /api/assets/import` - Import assets from CSV (Super Admin only)
- `GET /api/assets/:id/history` - Get asset history

### User Management Endpoints
- `POST /api/users/superadmin` - Create super admin (public, for initial setup)
- `POST /api/users` - Create new user (Super Admin only)

## 🧪 Testing

### Backend Tests
```bash
cd backend
npm test                    # Run all tests
npm run test:watch         # Run tests in watch mode
npm run test:coverage      # Run tests with coverage report
```

### Frontend Tests
```bash
cd frontend
npm test                   # Run all tests
npm run test:ui           # Run tests with UI
npm run test:coverage     # Run tests with coverage
```

## 🔧 Configuration

### Environment Variables

**Backend (.env)**
```env
MONGODB_URI=mongodb://localhost:27017/asset-management
PORT=5000
NODE_ENV=development
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE=1d
UPLOAD_PATH=uploads
MAX_FILE_SIZE=5000000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:3000
```

**Production Environment**

- Copy `.env.production` and update values
- Generate strong JWT secret
- Use secure MongoDB credentials
- Configure proper CORS origins

## 🗂️ Data Models

### Asset Model
```javascript
{
  assetTag: String (unique),     // e.g., "LAPTOP001"
  status: String,                // Available, Assigned, Maintenance, Retired
  manufacturer: String,          // e.g., "Dell", "HP"
  model: String,                 // e.g., "Latitude 5520"
  assetType: String,            // Desktop, Laptop, Server, Mobile
  serialNumber: String (unique), // Device serial number
  currentOwner: String,          // Employee name or department
  remarks: String,               // Additional notes
  history: Array,                // Change history
  createdAt: Date,
  updatedAt: Date
}
```

### User Model
```javascript
{
  username: String (unique),
  password: String (hashed),
  role: String,                  // superAdmin, user
  createdAt: Date,
  updatedAt: Date
}
```

## 📊 CSV Import Format

For bulk asset import, use the following CSV format:

```csv
assetTag,status,manufacturer,model,assetType,serialNumber,currentOwner,remarks
LAPTOP001,Available,Dell,Latitude 5520,Laptop,DL123456789,,New laptop for development
DESKTOP001,Assigned,HP,EliteDesk 800,Desktop,HP987654321,John Smith,Accounting department
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: Bcrypt with salt rounds
- **Rate Limiting**: API endpoint protection
- **CORS Configuration**: Cross-origin request security
- **Input Validation**: Request data validation
- **Security Headers**: Helmet.js security headers
- **File Upload Security**: Restricted file types and sizes
- **Role-Based Access**: Granular permission control

## 🚀 Deployment Options

### Development Deployment
```bash
docker-compose up --build
```

### Production Deployment
```bash
# Linux/Mac
./scripts/deploy.sh production

# Windows
docker-compose -f docker-compose.prod.yml up --build -d
```

### Cloud Deployment
The application is ready for deployment on:
- **AWS**: ECS, EC2, or Elastic Beanstalk
- **Google Cloud**: Cloud Run or Compute Engine
- **Azure**: Container Instances or App Service
- **DigitalOcean**: App Platform or Droplets

## 🔧 Maintenance

### Database Backup
```bash
# Linux/Mac
./scripts/backup.sh

# Windows
docker exec asset-management_mongodb_1 mongodump --db asset-management --gzip --archive=/tmp/backup.gz
```

### Database Restore
```bash
# Linux/Mac
./scripts/restore.sh backup_file.gz

# Windows
docker exec asset-management_mongodb_1 mongorestore --db asset-management --gzip --archive=/tmp/backup.gz
```

### Monitoring
- Health check endpoints: `/health`
- Docker health checks configured
- Application logs via `docker-compose logs`

## 🐛 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   netstat -tulpn | grep :3000
   # Kill the process or change ports in docker-compose.yml
   ```

2. **MongoDB Connection Issues**
   ```bash
   # Check MongoDB container status
   docker-compose ps mongodb
   # View MongoDB logs
   docker-compose logs mongodb
   ```

3. **Permission Issues (Linux/Mac)**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   chmod +x scripts/*.sh
   ```

4. **Build Failures**
   ```bash
   # Clean Docker cache
   docker system prune -a
   # Rebuild without cache
   docker-compose build --no-cache
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Run tests: `npm test`
5. Commit your changes: `git commit -am 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the troubleshooting section above
- Review the API documentation

## 🔄 Version History

- **v1.0.0** - Initial production-ready release
  - Complete asset management system
  - Role-based authentication
  - CSV import/export
  - Docker deployment
  - Comprehensive testing
  - Production security features

---

**Made with ❤️ for efficient asset management**
