import { Link } from 'react-router-dom'
import { useState, useEffect } from 'react'
import jwtDecode from 'jwt-decode'

const AssetCard = ({ asset }) => {
  const [isSuperAdmin, setIsSuperAdmin] = useState(false)

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      try {
        const decoded = jwtDecode(token)
        setIsSuperAdmin(decoded.role === 'superAdmin')
      } catch (error) {
        console.error('Error decoding token:', error)
        setIsSuperAdmin(false)
      }
    }
  }, [])

  return (
    <div className="asset-card">
      <div className="asset-header">
        <h3>{asset.assetTag}</h3>
        <span className={`status-badge status-${asset.status.toLowerCase()}`}>
          {asset.status}
        </span>
      </div>

      <div className="asset-details">
        <p><strong>Manufacturer:</strong> {asset.manufacturer}</p>
        <p><strong>Model:</strong> {asset.model}</p>
        <p><strong>Type:</strong> {asset.assetType}</p>
        <p><strong>Serial:</strong> {asset.serialNumber}</p>
        {asset.currentOwner && (
          <p><strong>Owner:</strong> {asset.currentOwner}</p>
        )}
      </div>

      <div className="asset-actions">
        <Link to={`/assets/${asset._id}`} className="btn btn-primary">
          View Details
        </Link>
        {isSuperAdmin && (
          <Link to={`/assets/edit/${asset._id}`} className="btn btn-secondary">
            Edit
          </Link>
        )}
      </div>
    </div>
  )
}

export default AssetCard
