import { Link } from 'react-router-dom'

const AssetCard = ({ asset }) => {
  return (
    <div className="asset-card">
      <h3>{asset.name}</h3>
      <p><strong>Category:</strong> {asset.category}</p>
      <p><strong>Status:</strong> {asset.status}</p>
      <p><strong>Location:</strong> {asset.location}</p>
      <div className="asset-actions">
        <Link to={`/assets/${asset._id}`} className="btn btn-primary">View</Link>
        <Link to={`/assets/edit/${asset._id}`} className="btn btn-secondary">Edit</Link>
      </div>
    </div>
  )
}

export default AssetCard
