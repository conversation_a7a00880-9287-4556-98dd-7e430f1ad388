import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import axios from 'axios'
import Login from '../pages/Login'

const renderLogin = () => {
  return render(
    <BrowserRouter>
      <Login />
    </BrowserRouter>
  )
}

describe('Login Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders login form', () => {
    renderLogin()
    
    expect(screen.getByLabelText(/username/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument()
  })

  it('handles successful login', async () => {
    const mockResponse = {
      data: {
        success: true,
        token: 'fake-token',
        user: { username: 'testuser', role: 'user' }
      }
    }
    axios.post.mockResolvedValueOnce(mockResponse)

    renderLogin()
    
    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: 'testuser' }
    })
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' }
    })
    
    fireEvent.click(screen.getByRole('button', { name: /login/i }))

    await waitFor(() => {
      expect(axios.post).toHaveBeenCalledWith('/api/auth/login', {
        username: 'testuser',
        password: 'password123'
      })
    })
  })

  it('displays error on failed login', async () => {
    axios.post.mockRejectedValueOnce(new Error('Login failed'))

    renderLogin()
    
    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: 'wronguser' }
    })
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'wrongpass' }
    })
    
    fireEvent.click(screen.getByRole('button', { name: /login/i }))

    await waitFor(() => {
      expect(screen.getByText(/invalid username or password/i)).toBeInTheDocument()
    })
  })

  it('validates required fields', () => {
    renderLogin()
    
    const usernameInput = screen.getByLabelText(/username/i)
    const passwordInput = screen.getByLabelText(/password/i)
    
    expect(usernameInput).toBeRequired()
    expect(passwordInput).toBeRequired()
  })
})
