# Quick Authentication Test
Write-Host "Testing Authentication..." -ForegroundColor Yellow

$body = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/login" -Method POST -Body $body -ContentType "application/json"
    
    if ($response.success) {
        Write-Host "✅ Authentication SUCCESS!" -ForegroundColor Green
        Write-Host "Token received: $($response.token.Substring(0,20))..." -ForegroundColor Cyan
        Write-Host "User: $($response.user.username)" -ForegroundColor Cyan
        Write-Host "Role: $($response.user.role)" -ForegroundColor Cyan
        
        # Test protected endpoint
        $headers = @{ "Authorization" = "Bearer $($response.token)" }
        $assetsResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/assets" -Method GET -Headers $headers
        
        if ($assetsResponse.success) {
            Write-Host "✅ Protected API SUCCESS!" -ForegroundColor Green
            Write-Host "Found $($assetsResponse.count) assets" -ForegroundColor Cyan
        } else {
            Write-Host "❌ Protected API FAILED" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Authentication FAILED" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
}
