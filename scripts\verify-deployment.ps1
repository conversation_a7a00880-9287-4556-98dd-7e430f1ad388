# Asset Management Application Deployment Verification Script for Windows
# This script verifies that the application is running correctly

Write-Host "🔍 Asset Management Application - Deployment Verification" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green

# Check if Docker is running
Write-Host "`n📋 Checking Docker status..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker is running: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running or not installed" -ForegroundColor Red
    exit 1
}

# Check if containers are running
Write-Host "`n📋 Checking container status..." -ForegroundColor Yellow
$containers = docker-compose ps --services
if ($containers) {
    Write-Host "✅ Docker Compose services found" -ForegroundColor Green
    docker-compose ps
} else {
    Write-Host "❌ No Docker Compose services running" -ForegroundColor Red
    Write-Host "Please run: docker-compose up --build" -ForegroundColor Yellow
    exit 1
}

# Wait for services to be ready
Write-Host "`n⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Test frontend
Write-Host "`n📋 Testing frontend..." -ForegroundColor Yellow
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend is accessible at http://localhost:3000" -ForegroundColor Green
    } else {
        Write-Host "❌ Frontend returned status code: $($frontendResponse.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Frontend is not accessible at http://localhost:3000" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test backend health
Write-Host "`n📋 Testing backend health..." -ForegroundColor Yellow
try {
    $backendResponse = Invoke-WebRequest -Uri "http://localhost:5000/health" -TimeoutSec 10
    if ($backendResponse.StatusCode -eq 200) {
        Write-Host "✅ Backend health check passed" -ForegroundColor Green
        $healthData = $backendResponse.Content | ConvertFrom-Json
        Write-Host "   Environment: $($healthData.environment)" -ForegroundColor Cyan
        Write-Host "   Timestamp: $($healthData.timestamp)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Backend health check failed with status: $($backendResponse.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Backend health check failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test backend API
Write-Host "`n📋 Testing backend API..." -ForegroundColor Yellow
try {
    $apiResponse = Invoke-WebRequest -Uri "http://localhost:5000/api" -TimeoutSec 10
    if ($apiResponse.StatusCode -eq 200) {
        Write-Host "❌ API root should return 404, but returned 200" -ForegroundColor Red
    }
} catch {
    if ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "✅ Backend API is responding correctly (404 for root)" -ForegroundColor Green
    } else {
        Write-Host "❌ Backend API test failed" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test database connection
Write-Host "`n📋 Testing database connection..." -ForegroundColor Yellow
try {
    $mongoStatus = docker-compose exec -T mongodb mongosh --eval "db.runCommand('ping')" --quiet
    if ($mongoStatus -match "ok.*1") {
        Write-Host "✅ MongoDB is running and accessible" -ForegroundColor Green
    } else {
        Write-Host "❌ MongoDB connection test failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Could not test MongoDB connection" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Check logs for errors
Write-Host "`n📋 Checking for errors in logs..." -ForegroundColor Yellow
$logs = docker-compose logs --tail=50
$errorLines = $logs | Select-String -Pattern "error|Error|ERROR|exception|Exception|EXCEPTION" | Select-Object -First 5

if ($errorLines) {
    Write-Host "⚠️  Found potential errors in logs:" -ForegroundColor Yellow
    $errorLines | ForEach-Object { Write-Host "   $_" -ForegroundColor Red }
} else {
    Write-Host "✅ No obvious errors found in recent logs" -ForegroundColor Green
}

# Summary
Write-Host "`n📊 Deployment Verification Summary" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host "🌐 Frontend URL: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🔧 Backend API: http://localhost:5000" -ForegroundColor Cyan
Write-Host "💾 Database: MongoDB (internal)" -ForegroundColor Cyan
Write-Host "`n🔐 Default Admin Credentials:" -ForegroundColor Yellow
Write-Host "   Username: admin" -ForegroundColor White
Write-Host "   Password: admin123" -ForegroundColor White
Write-Host "`n⚠️  IMPORTANT: Change the default password after first login!" -ForegroundColor Red

Write-Host "`n🎉 Deployment verification completed!" -ForegroundColor Green
Write-Host "You can now access the Asset Management Application at http://localhost:3000" -ForegroundColor Green
