Write-Host "Testing Asset Management Application..." -ForegroundColor Green

# Test Authentication
$body = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/login" -Method POST -Body $body -ContentType "application/json"
    
    if ($response.success) {
        Write-Host "SUCCESS: Authentication works!" -ForegroundColor Green
        
        # Test Asset API
        $headers = @{
            "Authorization" = "Bearer $($response.token)"
        }
        
        $assets = Invoke-RestMethod -Uri "http://localhost:5000/api/assets" -Headers $headers
        Write-Host "SUCCESS: Found $($assets.count) assets" -ForegroundColor Green
        
        Write-Host "`nApplication is working correctly!" -ForegroundColor Green
        Write-Host "Frontend: http://localhost:3000" -ForegroundColor Cyan
        Write-Host "Backend: http://localhost:5000" -ForegroundColor Cyan
        Write-Host "Admin credentials: admin / admin123" -ForegroundColor Yellow
        
    } else {
        Write-Host "FAIL: Authentication failed" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}
