const mongoose = require('mongoose');

const AssetHistorySchema = new mongoose.Schema({
  changedBy: {
    type: String,
    required: true
  },
  changeType: {
    type: String,
    enum: ['created', 'updated', 'deleted'],
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  snapshot: {
    assetTag: String,
    status: String,
    manufacturer: String,
    model: String,
    assetType: String,
    serialNumber: String,
    currentOwner: String,
    remarks: String
  }
});

const AssetSchema = new mongoose.Schema({
  assetTag: {
    type: String,
    required: [true, 'Asset tag is required'],
    trim: true,
    unique: true
  },
  status: {
    type: String,
    required: [true, 'Status is required'],
    enum: ['Available', 'Assigned', 'Maintenance', 'Retired'],
    default: 'Available'
  },
  manufacturer: {
    type: String,
    required: [true, 'Manufacturer is required'],
    trim: true
  },
  model: {
    type: String,
    required: [true, 'Model is required'],
    trim: true
  },
  assetType: {
    type: String,
    required: [true, 'Asset type is required'],
    enum: ['Desktop', 'Laptop', 'Server', 'Mobile']
  },
  serialNumber: {
    type: String,
    required: [true, 'Serial number is required'],
    trim: true,
    unique: true
  },
  currentOwner: {
    type: String,
    trim: true
  },
  remarks: {
    type: String,
    trim: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  history: [AssetHistorySchema]
});

// Update the updatedAt field on save
AssetSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Asset', AssetSchema);
