# Comprehensive Asset Management Application Test
Write-Host "🎯 COMPREHENSIVE APPLICATION TEST" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

$testResults = @()

# Test 1: Backend Health
Write-Host "`n📋 Test 1: Backend Health Check" -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:5000/health" -TimeoutSec 10
    if ($healthResponse.success) {
        Write-Host "✅ Backend Health: PASS" -ForegroundColor Green
        $testResults += @{ Test = "Backend Health"; Status = "PASS"; Details = "Service healthy" }
    } else {
        Write-Host "❌ Backend Health: FAIL" -ForegroundColor Red
        $testResults += @{ Test = "Backend Health"; Status = "FAIL"; Details = "Health check failed" }
    }
} catch {
    Write-Host "❌ Backend Health: ERROR - $($_.Exception.Message)" -ForegroundColor Red
    $testResults += @{ Test = "Backend Health"; Status = "FAIL"; Details = $_.Exception.Message }
}

# Test 2: Authentication
Write-Host "`n📋 Test 2: Authentication System" -ForegroundColor Yellow
try {
    $loginBody = @{
        username = "admin"
        password = "admin123"
    } | ConvertTo-Json
    
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json" -TimeoutSec 10
    
    if ($loginResponse.success -and $loginResponse.token) {
        Write-Host "✅ Authentication: PASS" -ForegroundColor Green
        $adminToken = $loginResponse.token
        $testResults += @{ Test = "Authentication"; Status = "PASS"; Details = "Admin login successful" }
        
        # Test token validation
        $headers = @{ "Authorization" = "Bearer $adminToken" }
        $meResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/me" -Method GET -Headers $headers -TimeoutSec 10
        
        if ($meResponse.success) {
            Write-Host "✅ Token Validation: PASS" -ForegroundColor Green
            $testResults += @{ Test = "Token Validation"; Status = "PASS"; Details = "JWT token valid" }
        } else {
            Write-Host "❌ Token Validation: FAIL" -ForegroundColor Red
            $testResults += @{ Test = "Token Validation"; Status = "FAIL"; Details = "Token validation failed" }
        }
    } else {
        Write-Host "❌ Authentication: FAIL" -ForegroundColor Red
        $testResults += @{ Test = "Authentication"; Status = "FAIL"; Details = "Login failed" }
        $adminToken = $null
    }
} catch {
    Write-Host "❌ Authentication: ERROR - $($_.Exception.Message)" -ForegroundColor Red
    $testResults += @{ Test = "Authentication"; Status = "FAIL"; Details = $_.Exception.Message }
    $adminToken = $null
}

# Test 3: Asset Management CRUD
Write-Host "`n📋 Test 3: Asset Management CRUD Operations" -ForegroundColor Yellow
if ($adminToken) {
    $headers = @{ "Authorization" = "Bearer $adminToken" }
    
    try {
        # Get existing assets
        $assetsResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/assets" -Method GET -Headers $headers -TimeoutSec 10
        
        if ($assetsResponse.success) {
            Write-Host "✅ Get Assets: PASS ($($assetsResponse.count) assets found)" -ForegroundColor Green
            $testResults += @{ Test = "Get Assets"; Status = "PASS"; Details = "Found $($assetsResponse.count) assets" }
            
            # Create a test asset
            $testAsset = @{
                assetTag = "TEST-$(Get-Date -Format 'yyyyMMddHHmmss')"
                status = "Available"
                manufacturer = "Test Corp"
                model = "Test Model"
                assetType = "Laptop"
                serialNumber = "TST$(Get-Random -Minimum 100000 -Maximum 999999)"
                currentOwner = ""
                remarks = "Created by comprehensive test"
            } | ConvertTo-Json
            
            $createResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/assets" -Method POST -Body $testAsset -ContentType "application/json" -Headers $headers -TimeoutSec 10
            
            if ($createResponse.success) {
                Write-Host "✅ Create Asset: PASS" -ForegroundColor Green
                $assetId = $createResponse.data._id
                $assetTag = $createResponse.data.assetTag
                $testResults += @{ Test = "Create Asset"; Status = "PASS"; Details = "Created asset $assetTag" }
                
                # Update the asset
                $updateData = @{
                    status = "Assigned"
                    currentOwner = "Test User"
                    remarks = "Updated by comprehensive test"
                } | ConvertTo-Json
                
                $updateResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/assets/$assetId" -Method PUT -Body $updateData -ContentType "application/json" -Headers $headers -TimeoutSec 10
                
                if ($updateResponse.success) {
                    Write-Host "✅ Update Asset: PASS" -ForegroundColor Green
                    $testResults += @{ Test = "Update Asset"; Status = "PASS"; Details = "Asset updated successfully" }
                } else {
                    Write-Host "❌ Update Asset: FAIL" -ForegroundColor Red
                    $testResults += @{ Test = "Update Asset"; Status = "FAIL"; Details = "Update failed" }
                }
                
                # Get asset history
                $historyResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/assets/$assetId/history" -Method GET -Headers $headers -TimeoutSec 10
                
                if ($historyResponse.success) {
                    Write-Host "✅ Asset History: PASS ($($historyResponse.history.Count) entries)" -ForegroundColor Green
                    $testResults += @{ Test = "Asset History"; Status = "PASS"; Details = "$($historyResponse.history.Count) history entries" }
                } else {
                    Write-Host "❌ Asset History: FAIL" -ForegroundColor Red
                    $testResults += @{ Test = "Asset History"; Status = "FAIL"; Details = "History retrieval failed" }
                }
                
                # Delete test asset
                $deleteResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/assets/$assetId" -Method DELETE -Headers $headers -TimeoutSec 10
                
                if ($deleteResponse.success) {
                    Write-Host "✅ Delete Asset: PASS" -ForegroundColor Green
                    $testResults += @{ Test = "Delete Asset"; Status = "PASS"; Details = "Asset deleted successfully" }
                } else {
                    Write-Host "❌ Delete Asset: FAIL" -ForegroundColor Red
                    $testResults += @{ Test = "Delete Asset"; Status = "FAIL"; Details = "Delete failed" }
                }
            } else {
                Write-Host "❌ Create Asset: FAIL" -ForegroundColor Red
                $testResults += @{ Test = "Create Asset"; Status = "FAIL"; Details = "Asset creation failed" }
            }
        } else {
            Write-Host "❌ Get Assets: FAIL" -ForegroundColor Red
            $testResults += @{ Test = "Get Assets"; Status = "FAIL"; Details = "Failed to retrieve assets" }
        }
    } catch {
        Write-Host "❌ Asset Management: ERROR - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{ Test = "Asset Management"; Status = "FAIL"; Details = $_.Exception.Message }
    }
} else {
    Write-Host "⚠️  Skipping Asset Management tests - no admin token" -ForegroundColor Yellow
    $testResults += @{ Test = "Asset Management"; Status = "SKIP"; Details = "No admin token available" }
}

# Test 4: Security & Access Control
Write-Host "`n📋 Test 4: Security & Access Control" -ForegroundColor Yellow
try {
    # Test unauthorized access
    $unauthorizedResponse = Invoke-WebRequest -Uri "http://localhost:5000/api/assets" -TimeoutSec 5 -ErrorAction SilentlyContinue
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ Unauthorized Access Protection: PASS" -ForegroundColor Green
        $testResults += @{ Test = "Security"; Status = "PASS"; Details = "Unauthorized access properly blocked" }
    } else {
        Write-Host "❌ Unauthorized Access Protection: FAIL" -ForegroundColor Red
        $testResults += @{ Test = "Security"; Status = "FAIL"; Details = "Security check failed" }
    }
}

# Test 5: Frontend Accessibility
Write-Host "`n📋 Test 5: Frontend Accessibility" -ForegroundColor Yellow
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend Access: PASS" -ForegroundColor Green
        $testResults += @{ Test = "Frontend"; Status = "PASS"; Details = "Frontend accessible" }
    } else {
        Write-Host "❌ Frontend Access: FAIL - Status: $($frontendResponse.StatusCode)" -ForegroundColor Red
        $testResults += @{ Test = "Frontend"; Status = "FAIL"; Details = "Status: $($frontendResponse.StatusCode)" }
    }
} catch {
    Write-Host "⚠️  Frontend Access: WARNING - $($_.Exception.Message)" -ForegroundColor Yellow
    $testResults += @{ Test = "Frontend"; Status = "WARNING"; Details = $_.Exception.Message }
}

# Test Summary
Write-Host "`n🎉 COMPREHENSIVE TEST RESULTS" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

$passCount = ($testResults | Where-Object { $_.Status -eq "PASS" }).Count
$failCount = ($testResults | Where-Object { $_.Status -eq "FAIL" }).Count
$warnCount = ($testResults | Where-Object { $_.Status -eq "WARNING" }).Count
$skipCount = ($testResults | Where-Object { $_.Status -eq "SKIP" }).Count
$totalTests = $testResults.Count

Write-Host "`nTest Summary:" -ForegroundColor White
Write-Host "  Total Tests: $totalTests" -ForegroundColor White
Write-Host "  Passed: $passCount" -ForegroundColor Green
Write-Host "  Failed: $failCount" -ForegroundColor Red
Write-Host "  Warnings: $warnCount" -ForegroundColor Yellow
Write-Host "  Skipped: $skipCount" -ForegroundColor Gray

Write-Host "`nDetailed Results:" -ForegroundColor Yellow
foreach ($result in $testResults) {
    $color = switch ($result.Status) {
        "PASS" { "Green" }
        "FAIL" { "Red" }
        "WARNING" { "Yellow" }
        "SKIP" { "Gray" }
        default { "White" }
    }
    $symbol = switch ($result.Status) {
        "PASS" { "✅" }
        "FAIL" { "❌" }
        "WARNING" { "⚠️ " }
        "SKIP" { "⏭️ " }
        default { "❓" }
    }
    Write-Host "$symbol $($result.Test): $($result.Status)" -ForegroundColor $color
    Write-Host "   $($result.Details)" -ForegroundColor Gray
}

if ($failCount -eq 0) {
    Write-Host "`n🎉 ALL CRITICAL TESTS PASSED!" -ForegroundColor Green
    Write-Host "The Asset Management Application is fully functional and production-ready!" -ForegroundColor Green
    
    Write-Host "`n🌐 Application URLs:" -ForegroundColor Cyan
    Write-Host "   Frontend: http://localhost:3000" -ForegroundColor White
    Write-Host "   Backend API: http://localhost:5000" -ForegroundColor White
    Write-Host "   Health Check: http://localhost:5000/health" -ForegroundColor White
    
    Write-Host "`n🔐 Default Admin Credentials:" -ForegroundColor Yellow
    Write-Host "   Username: admin" -ForegroundColor White
    Write-Host "   Password: admin123" -ForegroundColor White
    
    Write-Host "`n⚠️  IMPORTANT SECURITY REMINDERS:" -ForegroundColor Red
    Write-Host "   1. Change the default admin password immediately!" -ForegroundColor White
    Write-Host "   2. Update JWT_SECRET in production environment" -ForegroundColor White
    Write-Host "   3. Configure HTTPS for production deployment" -ForegroundColor White
    Write-Host "   4. Set up regular database backups" -ForegroundColor White
    
    Write-Host "`n🚀 DEPLOYMENT STATUS: READY FOR PRODUCTION!" -ForegroundColor Green
} else {
    Write-Host "`n❌ Some tests failed. Please review the issues above." -ForegroundColor Red
    Write-Host "Check logs with: docker-compose logs" -ForegroundColor Yellow
}

Write-Host "`n✨ Test completed successfully!" -ForegroundColor Green
