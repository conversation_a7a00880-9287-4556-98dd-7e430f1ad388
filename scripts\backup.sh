#!/bin/bash

# Asset Management Database Backup Script

set -e

# Configuration
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="asset_management_backup_$DATE.gz"
CONTAINER_NAME="asset-management_mongodb_1"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

echo "🗄️  Starting database backup..."

# Check if MongoDB container is running
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo "❌ MongoDB container is not running. Please start the application first."
    exit 1
fi

# Create backup
echo "📦 Creating backup: $BACKUP_FILE"
docker exec $CONTAINER_NAME mongodump --db asset-management --gzip --archive=/tmp/$BACKUP_FILE

# Copy backup from container to host
docker cp $CONTAINER_NAME:/tmp/$BACKUP_FILE $BACKUP_DIR/$BACKUP_FILE

# Remove backup from container
docker exec $CONTAINER_NAME rm /tmp/$BACKUP_FILE

# Cleanup old backups (keep last 30 days)
echo "🧹 Cleaning up old backups..."
find $BACKUP_DIR -name "asset_management_backup_*.gz" -mtime +30 -delete

echo "✅ Backup completed successfully!"
echo "📁 Backup saved to: $BACKUP_DIR/$BACKUP_FILE"

# Show backup size
BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_FILE" | cut -f1)
echo "📊 Backup size: $BACKUP_SIZE"
