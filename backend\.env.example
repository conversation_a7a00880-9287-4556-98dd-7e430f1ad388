# Database Configuration
MONGODB_URI=mongodb://localhost:27017/asset-management

# Server Configuration
PORT=5000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=1d

# File Upload Configuration
UPLOAD_PATH=uploads
MAX_FILE_SIZE=5000000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
