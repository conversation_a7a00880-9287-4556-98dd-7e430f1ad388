import { render, screen } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import AssetCard from '../components/AssetCard'

const mockAsset = {
  _id: '123',
  assetTag: 'LAPTOP001',
  status: 'Available',
  manufacturer: 'Dell',
  model: 'Latitude 5520',
  assetType: 'Laptop',
  serialNumber: 'DL123456',
  currentOwner: '<PERSON>'
}

const renderAssetCard = (asset = mockAsset) => {
  return render(
    <BrowserRouter>
      <AssetCard asset={asset} />
    </BrowserRouter>
  )
}

describe('AssetCard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.getItem.mockReturnValue('fake-token')
  })

  it('renders asset information correctly', () => {
    renderAssetCard()
    
    expect(screen.getByText('LAPTOP001')).toBeInTheDocument()
    expect(screen.getByText('Available')).toBeInTheDocument()
    expect(screen.getByText('Dell')).toBeInTheDocument()
    expect(screen.getByText('Latitude 5520')).toBeInTheDocument()
    expect(screen.getByText('Laptop')).toBeInTheDocument()
    expect(screen.getByText('DL123456')).toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
  })

  it('shows view details link', () => {
    renderAssetCard()
    
    const viewLink = screen.getByText('View Details')
    expect(viewLink).toBeInTheDocument()
    expect(viewLink.closest('a')).toHaveAttribute('href', '/assets/123')
  })

  it('shows edit link for super admin', () => {
    // Mock super admin token
    const jwtDecode = require('jwt-decode').default
    jwtDecode.mockReturnValue({ role: 'superAdmin' })
    
    renderAssetCard()
    
    expect(screen.getByText('Edit')).toBeInTheDocument()
  })

  it('hides edit link for regular user', () => {
    // Mock regular user token
    const jwtDecode = require('jwt-decode').default
    jwtDecode.mockReturnValue({ role: 'user' })
    
    renderAssetCard()
    
    expect(screen.queryByText('Edit')).not.toBeInTheDocument()
  })

  it('applies correct status badge class', () => {
    renderAssetCard()
    
    const statusBadge = screen.getByText('Available')
    expect(statusBadge).toHaveClass('status-badge', 'status-available')
  })

  it('handles asset without owner', () => {
    const assetWithoutOwner = { ...mockAsset, currentOwner: null }
    renderAssetCard(assetWithoutOwner)
    
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
  })
})
