{"name": "asset-management-backend", "version": "1.0.0", "description": "Backend for Asset Management Application", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.0.3", "csv-parser": "^3.0.0", "multer": "^1.4.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "helmet": "^6.1.5", "express-rate-limit": "^6.7.0", "express-validator": "^6.15.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "@types/jest": "^29.5.1", "mongodb-memory-server": "^8.12.2"}}