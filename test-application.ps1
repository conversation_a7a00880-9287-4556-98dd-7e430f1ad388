# Asset Management Application - Comprehensive Test Script
# This script tests the complete application functionality

Write-Host "🧪 Asset Management Application - Comprehensive Testing" -ForegroundColor Green
Write-Host "=======================================================" -ForegroundColor Green

# Function to make HTTP requests
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Uri,
        [hashtable]$Headers = @{},
        [object]$Body = $null
    )
    
    try {
        $params = @{
            Method = $Method
            Uri = $Uri
            Headers = $Headers
            TimeoutSec = 30
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json)
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-RestMethod @params
        return @{ Success = $true; Data = $response; StatusCode = 200 }
    }
    catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
        return @{ Success = $false; Error = $_.Exception.Message; StatusCode = $statusCode }
    }
}

# Test Results
$testResults = @()

Write-Host "`n📋 Step 1: Starting Application..." -ForegroundColor Yellow
try {
    $buildResult = docker-compose up --build -d
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to start application with docker-compose" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Application containers started" -ForegroundColor Green
} catch {
    Write-Host "❌ Error starting application: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 45

Write-Host "`n📋 Step 2: Testing Service Health..." -ForegroundColor Yellow

# Test Backend Health
$healthTest = Invoke-ApiRequest -Method "GET" -Uri "http://localhost:5000/health"
if ($healthTest.Success) {
    Write-Host "✅ Backend health check passed" -ForegroundColor Green
    $testResults += @{ Test = "Backend Health"; Status = "PASS"; Details = "Service is healthy" }
} else {
    Write-Host "❌ Backend health check failed: $($healthTest.Error)" -ForegroundColor Red
    $testResults += @{ Test = "Backend Health"; Status = "FAIL"; Details = $healthTest.Error }
}

# Test Frontend
try {
    $frontendTest = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10
    if ($frontendTest.StatusCode -eq 200) {
        Write-Host "✅ Frontend is accessible" -ForegroundColor Green
        $testResults += @{ Test = "Frontend Access"; Status = "PASS"; Details = "Frontend loaded successfully" }
    }
} catch {
    Write-Host "❌ Frontend test failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += @{ Test = "Frontend Access"; Status = "FAIL"; Details = $_.Exception.Message }
}

Write-Host "`n📋 Step 3: Testing Authentication..." -ForegroundColor Yellow

# Test Login with default admin credentials
$loginTest = Invoke-ApiRequest -Method "POST" -Uri "http://localhost:5000/api/auth/login" -Body @{
    username = "admin"
    password = "admin123"
}

if ($loginTest.Success -and $loginTest.Data.success) {
    Write-Host "✅ Admin login successful" -ForegroundColor Green
    $adminToken = $loginTest.Data.token
    $testResults += @{ Test = "Admin Login"; Status = "PASS"; Details = "Default admin credentials work" }
    
    # Test token validation
    $meTest = Invoke-ApiRequest -Method "GET" -Uri "http://localhost:5000/api/auth/me" -Headers @{
        "Authorization" = "Bearer $adminToken"
    }
    
    if ($meTest.Success) {
        Write-Host "✅ Token validation successful" -ForegroundColor Green
        $testResults += @{ Test = "Token Validation"; Status = "PASS"; Details = "JWT token works correctly" }
    } else {
        Write-Host "❌ Token validation failed" -ForegroundColor Red
        $testResults += @{ Test = "Token Validation"; Status = "FAIL"; Details = $meTest.Error }
    }
} else {
    Write-Host "❌ Admin login failed: $($loginTest.Error)" -ForegroundColor Red
    $testResults += @{ Test = "Admin Login"; Status = "FAIL"; Details = $loginTest.Error }
    $adminToken = $null
}

Write-Host "`n📋 Step 4: Testing Asset Management..." -ForegroundColor Yellow

if ($adminToken) {
    $authHeaders = @{ "Authorization" = "Bearer $adminToken" }
    
    # Test Get Assets
    $getAssetsTest = Invoke-ApiRequest -Method "GET" -Uri "http://localhost:5000/api/assets" -Headers $authHeaders
    if ($getAssetsTest.Success) {
        Write-Host "✅ Get assets successful" -ForegroundColor Green
        $assetCount = $getAssetsTest.Data.data.Count
        Write-Host "   Found $assetCount existing assets" -ForegroundColor Cyan
        $testResults += @{ Test = "Get Assets"; Status = "PASS"; Details = "Retrieved $assetCount assets" }
    } else {
        Write-Host "❌ Get assets failed: $($getAssetsTest.Error)" -ForegroundColor Red
        $testResults += @{ Test = "Get Assets"; Status = "FAIL"; Details = $getAssetsTest.Error }
    }
    
    # Test Create Asset
    $newAsset = @{
        assetTag = "TEST001"
        status = "Available"
        manufacturer = "Test Corp"
        model = "Test Model"
        assetType = "Laptop"
        serialNumber = "TST123456789"
        currentOwner = ""
        remarks = "Test asset created by automated test"
    }
    
    $createAssetTest = Invoke-ApiRequest -Method "POST" -Uri "http://localhost:5000/api/assets" -Headers $authHeaders -Body $newAsset
    if ($createAssetTest.Success) {
        Write-Host "✅ Create asset successful" -ForegroundColor Green
        $createdAssetId = $createAssetTest.Data.data._id
        $testResults += @{ Test = "Create Asset"; Status = "PASS"; Details = "Asset TEST001 created successfully" }
        
        # Test Update Asset
        $updateData = @{
            status = "Assigned"
            currentOwner = "Test User"
            remarks = "Updated by automated test"
        }
        
        $updateAssetTest = Invoke-ApiRequest -Method "PUT" -Uri "http://localhost:5000/api/assets/$createdAssetId" -Headers $authHeaders -Body $updateData
        if ($updateAssetTest.Success) {
            Write-Host "✅ Update asset successful" -ForegroundColor Green
            $testResults += @{ Test = "Update Asset"; Status = "PASS"; Details = "Asset updated successfully" }
        } else {
            Write-Host "❌ Update asset failed: $($updateAssetTest.Error)" -ForegroundColor Red
            $testResults += @{ Test = "Update Asset"; Status = "FAIL"; Details = $updateAssetTest.Error }
        }
        
        # Test Get Asset History
        $historyTest = Invoke-ApiRequest -Method "GET" -Uri "http://localhost:5000/api/assets/$createdAssetId/history" -Headers $authHeaders
        if ($historyTest.Success) {
            Write-Host "✅ Get asset history successful" -ForegroundColor Green
            $historyCount = $historyTest.Data.history.Count
            $testResults += @{ Test = "Asset History"; Status = "PASS"; Details = "Retrieved $historyCount history entries" }
        } else {
            Write-Host "❌ Get asset history failed: $($historyTest.Error)" -ForegroundColor Red
            $testResults += @{ Test = "Asset History"; Status = "FAIL"; Details = $historyTest.Error }
        }
        
        # Test Delete Asset
        $deleteAssetTest = Invoke-ApiRequest -Method "DELETE" -Uri "http://localhost:5000/api/assets/$createdAssetId" -Headers $authHeaders
        if ($deleteAssetTest.Success) {
            Write-Host "✅ Delete asset successful" -ForegroundColor Green
            $testResults += @{ Test = "Delete Asset"; Status = "PASS"; Details = "Asset deleted successfully" }
        } else {
            Write-Host "❌ Delete asset failed: $($deleteAssetTest.Error)" -ForegroundColor Red
            $testResults += @{ Test = "Delete Asset"; Status = "FAIL"; Details = $deleteAssetTest.Error }
        }
    } else {
        Write-Host "❌ Create asset failed: $($createAssetTest.Error)" -ForegroundColor Red
        $testResults += @{ Test = "Create Asset"; Status = "FAIL"; Details = $createAssetTest.Error }
    }
} else {
    Write-Host "⚠️  Skipping asset tests - no admin token available" -ForegroundColor Yellow
}

Write-Host "`n📋 Step 5: Testing Role-Based Access..." -ForegroundColor Yellow

# Test unauthorized access
$unauthorizedTest = Invoke-ApiRequest -Method "GET" -Uri "http://localhost:5000/api/assets"
if ($unauthorizedTest.StatusCode -eq 401) {
    Write-Host "✅ Unauthorized access properly blocked" -ForegroundColor Green
    $testResults += @{ Test = "Unauthorized Access"; Status = "PASS"; Details = "API properly requires authentication" }
} else {
    Write-Host "❌ Unauthorized access not properly blocked" -ForegroundColor Red
    $testResults += @{ Test = "Unauthorized Access"; Status = "FAIL"; Details = "API should require authentication" }
}

Write-Host "`n📊 Test Results Summary" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green

$passCount = ($testResults | Where-Object { $_.Status -eq "PASS" }).Count
$failCount = ($testResults | Where-Object { $_.Status -eq "FAIL" }).Count
$totalTests = $testResults.Count

Write-Host "`nTotal Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red

Write-Host "`nDetailed Results:" -ForegroundColor Yellow
foreach ($result in $testResults) {
    $color = if ($result.Status -eq "PASS") { "Green" } else { "Red" }
    $symbol = if ($result.Status -eq "PASS") { "✅" } else { "❌" }
    Write-Host "$symbol $($result.Test): $($result.Status)" -ForegroundColor $color
    Write-Host "   $($result.Details)" -ForegroundColor Gray
}

if ($failCount -eq 0) {
    Write-Host "`n🎉 ALL TESTS PASSED! Application is working correctly." -ForegroundColor Green
    Write-Host "`n🌐 Application URLs:" -ForegroundColor Cyan
    Write-Host "   Frontend: http://localhost:3000" -ForegroundColor White
    Write-Host "   Backend API: http://localhost:5000" -ForegroundColor White
    Write-Host "`n🔐 Default Admin Credentials:" -ForegroundColor Yellow
    Write-Host "   Username: admin" -ForegroundColor White
    Write-Host "   Password: admin123" -ForegroundColor White
    Write-Host "`n⚠️  Remember to change the default password!" -ForegroundColor Red
} else {
    Write-Host "`n❌ Some tests failed. Please check the application configuration." -ForegroundColor Red
    Write-Host "Check logs with: docker-compose logs" -ForegroundColor Yellow
}

Write-Host "`n📋 Application is ready for use!" -ForegroundColor Green
