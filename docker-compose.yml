# Docker Compose for Asset Management Application

services:
  frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    environment:
      - VITE_API_URL=http://localhost:5000/api
    restart: unless-stopped
    networks:
      - asset-management-network

  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    ports:
      - "5000:5000"
    depends_on:
      mongodb:
        condition: service_healthy
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/asset-management
      - PORT=5000
      - NODE_ENV=development
      - JWT_SECRET=development-jwt-secret-change-this-in-production
      - JWT_EXPIRE=1d
      - UPLOAD_PATH=uploads
      - MAX_FILE_SIZE=5000000
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - backend_uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - asset-management-network

  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/asset-management --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - asset-management-network

volumes:
  mongodb_data:
    driver: local
  backend_uploads:
    driver: local

networks:
  asset-management-network:
    driver: bridge
