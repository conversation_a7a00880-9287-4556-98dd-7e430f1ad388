#!/bin/bash

# Asset Management Database Restore Script

set -e

# Check if backup file is provided
if [ $# -eq 0 ]; then
    echo "❌ Please provide a backup file to restore."
    echo "Usage: $0 <backup_file.gz>"
    echo ""
    echo "Available backups:"
    ls -la ./backups/asset_management_backup_*.gz 2>/dev/null || echo "No backups found."
    exit 1
fi

BACKUP_FILE=$1
CONTAINER_NAME="asset-management_mongodb_1"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Check if MongoDB container is running
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo "❌ MongoDB container is not running. Please start the application first."
    exit 1
fi

echo "🔄 Starting database restore from: $BACKUP_FILE"

# Confirm restore operation
read -p "⚠️  This will replace all existing data. Are you sure? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Restore cancelled."
    exit 1
fi

# Copy backup to container
echo "📤 Copying backup to container..."
docker cp "$BACKUP_FILE" $CONTAINER_NAME:/tmp/restore_backup.gz

# Drop existing database
echo "🗑️  Dropping existing database..."
docker exec $CONTAINER_NAME mongosh --eval "db.getSiblingDB('asset-management').dropDatabase()"

# Restore database
echo "📥 Restoring database..."
docker exec $CONTAINER_NAME mongorestore --db asset-management --gzip --archive=/tmp/restore_backup.gz

# Remove backup from container
docker exec $CONTAINER_NAME rm /tmp/restore_backup.gz

echo "✅ Database restore completed successfully!"
echo "🔄 Please restart the application to ensure all services are synchronized."
